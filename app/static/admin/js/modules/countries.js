/**
 * Модуль управления странами
 * Обеспечивает функциональность для работы со странами в админ панели
 */
class CountriesModule {
    constructor() {
        this.isLoading = false;
        this.currentCountries = [];
        this.editingCountryId = null;
    }

    /**
     * Инициализация модуля стран
     */
    init() {
        console.log('Инициализация модуля стран');
    }

    /**
     * Загрузка списка стран
     */
    async loadCountries() {
        if (this.isLoading) return;
        
        console.log('Загрузка стран');
        this.isLoading = true;

        try {
            // Показываем индикатор загрузки
            this.showLoadingIndicator();

            // Загружаем страны через API
            const countries = await apiClient.get(AppConstants.API_ENDPOINTS.COUNTRIES);
            console.log('Страны получены:', countries);

            this.currentCountries = countries;
            this.renderCountries(countries);

        } catch (error) {
            console.error('Ошибка загрузки стран:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Отображение индикатора загрузки
     */
    showLoadingIndicator() {
        const container = document.getElementById('countries-content');
        if (container) {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <span class="ms-2">Загрузка стран...</span>
                </div>
            `;
        }
    }

    /**
     * Обработка ошибки загрузки
     * @param {Error} error - Ошибка загрузки
     */
    handleLoadError(error) {
        const container = document.getElementById('countries-content');
        if (container) {
            let errorMessage = 'Ошибка загрузки данных';
            
            if (error.message.includes('Не авторизован')) {
                errorMessage = 'Ошибка авторизации. Пожалуйста, войдите в систему заново.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Ошибка соединения с сервером';
            }

            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Ошибка загрузки стран:</strong> ${errorMessage}
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="countriesModule.loadCountries()">
                        <i class="bi bi-arrow-clockwise"></i> Повторить
                    </button>
                </div>
            `;
        }

        // Показываем уведомление об ошибке
        if (window.uiManager) {
            uiManager.showError(`Ошибка загрузки стран: ${error.message}`);
        }
    }

    /**
     * Отображение списка стран
     * @param {Array} countries - Массив стран
     */
    renderCountries(countries) {
        const container = document.getElementById('countries-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-globe"></i> Список стран</h5>
                    <button class="btn btn-primary" onclick="countriesModule.showCountryModal()">
                        <i class="bi bi-plus"></i> Добавить страну
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Код</th>
                                    <th>Название</th>
                                    <th>Активна</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${countries.map(country => `
                                    <tr>
                                        <td>${country.id}</td>
                                        <td><code>${country.code}</code></td>
                                        <td>${escapeHtml(country.name)}</td>
                                        <td>
                                            <span class="badge bg-${getBooleanColor(country.is_active)}">
                                                ${formatBoolean(country.is_active)}
                                            </span>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary me-1" 
                                                    onclick="countriesModule.editCountry(${country.id})" 
                                                    title="Редактировать">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="countriesModule.deleteCountry(${country.id})" 
                                                    title="Удалить">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ${countries.length === 0 ? `
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-globe" style="font-size: 3rem;"></i>
                            <p class="mt-2">Страны не найдены</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * Показать модальное окно для создания/редактирования страны
     * @param {number|null} countryId - ID страны для редактирования (null для создания)
     */
    showCountryModal(countryId = null) {
        this.editingCountryId = countryId;
        const isEditing = countryId !== null;
        
        const modalHtml = `
            <div class="modal fade" id="countryModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-globe"></i>
                                ${isEditing ? 'Редактировать страну' : 'Добавить страну'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="countryForm">
                                <div class="mb-3">
                                    <label for="countryCode" class="form-label">Код страны *</label>
                                    <input type="text" class="form-control" id="countryCode" 
                                           placeholder="Например: 0" required>
                                    <div class="form-text">Числовой код страны для SMS провайдеров</div>
                                </div>
                                <div class="mb-3">
                                    <label for="countryName" class="form-label">Название страны *</label>
                                    <input type="text" class="form-control" id="countryName" 
                                           placeholder="Например: Россия" required>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="countryActive" checked>
                                        <label class="form-check-label" for="countryActive">
                                            Активна
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="countriesModule.saveCountry()">
                                ${isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('countryModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные страны
        if (isEditing) {
            this.loadCountryData(countryId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('countryModal'));
        modal.show();
    }

    /**
     * Загрузка данных страны для редактирования
     * @param {number} countryId - ID страны
     */
    async loadCountryData(countryId) {
        try {
            const country = this.currentCountries.find(c => c.id === countryId);
            if (country) {
                document.getElementById('countryCode').value = country.code;
                document.getElementById('countryName').value = country.name;
                document.getElementById('countryActive').checked = country.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных страны:', error);
            uiManager.showError('Ошибка загрузки данных страны');
        }
    }

    /**
     * Сохранение страны (создание или обновление)
     */
    async saveCountry() {
        try {
            const form = document.getElementById('countryForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const countryData = {
                code: parseInt(document.getElementById('countryCode').value),
                name: document.getElementById('countryName').value.trim(),
                is_active: document.getElementById('countryActive').checked
            };

            console.log('Сохранение страны:', countryData);

            let result;
            if (this.editingCountryId) {
                // Обновляем существующую страну
                result = await apiClient.put(
                    AppConstants.API_ENDPOINTS.COUNTRY_BY_ID(this.editingCountryId),
                    countryData
                );
                uiManager.showSuccess('Страна успешно обновлена');
            } else {
                // Создаем новую страну
                result = await apiClient.post(AppConstants.API_ENDPOINTS.COUNTRIES, countryData);
                uiManager.showSuccess('Страна успешно создана');
            }

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('countryModal'));
            modal.hide();

            // Перезагружаем список стран
            await this.loadCountries();

        } catch (error) {
            console.error('Ошибка сохранения страны:', error);
            uiManager.showError(`Ошибка сохранения страны: ${error.message}`);
        }
    }

    /**
     * Редактирование страны
     * @param {number} countryId - ID страны
     */
    editCountry(countryId) {
        this.showCountryModal(countryId);
    }

    /**
     * Удаление страны
     * @param {number} countryId - ID страны
     */
    async deleteCountry(countryId) {
        const country = this.currentCountries.find(c => c.id === countryId);
        if (!country) return;

        const confirmed = await new Promise((resolve) => {
            uiManager.showConfirmModal(
                'Подтверждение удаления',
                `Вы уверены, что хотите удалить страну "${country.name}"?<br><br>
                <strong>Внимание:</strong> Удаление возможно только если у страны нет связанных цен или активаций.`,
                () => resolve(true),
                () => resolve(false)
            );
        });

        if (!confirmed) return;

        try {
            await apiClient.delete(AppConstants.API_ENDPOINTS.COUNTRY_BY_ID(countryId));
            uiManager.showSuccess('Страна успешно удалена');
            
            // Перезагружаем список стран
            await this.loadCountries();

        } catch (error) {
            console.error('Ошибка удаления страны:', error);
            uiManager.showError(`Ошибка удаления страны: ${error.message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля стран
window.countriesModule = new CountriesModule();
