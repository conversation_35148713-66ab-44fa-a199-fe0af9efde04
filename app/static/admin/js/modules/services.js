/**
 * Модуль управления сервисами
 * Обеспечивает функциональность для работы с сервисами в админ панели
 */
class ServicesModule {
    constructor() {
        this.isLoading = false;
        this.currentServices = [];
        this.editingServiceId = null;
    }

    /**
     * Инициализация модуля сервисов
     */
    init() {
        console.log('Инициализация модуля сервисов');
    }

    /**
     * Показать модальное окно для создания/редактирования сервиса
     * @param {number|null} serviceId - ID сервиса для редактирования (null для создания)
     */
    showServiceModal(serviceId = null) {
        this.editingServiceId = serviceId;
        const isEditing = serviceId !== null;
        
        const modalHtml = `
            <div class="modal fade" id="serviceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-gear"></i>
                                ${isEditing ? 'Редактировать сервис' : 'Добавить сервис'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="serviceForm">
                                <div class="mb-3">
                                    <label for="serviceCode" class="form-label">Код сервиса *</label>
                                    <input type="text" class="form-control" id="serviceCode" 
                                           placeholder="Например: telegram" required>
                                    <div class="form-text">Уникальный код сервиса для SMS провайдеров</div>
                                </div>
                                <div class="mb-3">
                                    <label for="serviceName" class="form-label">Название сервиса *</label>
                                    <input type="text" class="form-control" id="serviceName" 
                                           placeholder="Например: Telegram" required>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="serviceActive" checked>
                                        <label class="form-check-label" for="serviceActive">
                                            Активен
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="servicesModule.saveService()">
                                ${isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('serviceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные сервиса
        if (isEditing) {
            this.loadServiceData(serviceId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
        modal.show();
    }

    /**
     * Загрузка данных сервиса для редактирования
     * @param {number} serviceId - ID сервиса
     */
    async loadServiceData(serviceId) {
        try {
            // Получаем данные сервиса из текущего списка или загружаем с сервера
            let service = null;
            
            if (window.adminPanel && window.adminPanel.servicesPagination) {
                // Пытаемся найти в текущих данных пагинации
                const currentData = window.adminPanel.servicesPagination.currentData;
                if (currentData) {
                    service = currentData.find(s => s.id === serviceId);
                }
            }
            
            if (!service) {
                // Загружаем с сервера
                service = await apiClient.get(AppConstants.API_ENDPOINTS.SERVICE_BY_ID(serviceId));
            }

            if (service) {
                document.getElementById('serviceCode').value = service.code;
                document.getElementById('serviceName').value = service.name;
                document.getElementById('serviceActive').checked = service.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных сервиса:', error);
            uiManager.showError('Ошибка загрузки данных сервиса');
        }
    }

    /**
     * Сохранение сервиса (создание или обновление)
     */
    async saveService() {
        try {
            const form = document.getElementById('serviceForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const serviceData = {
                code: document.getElementById('serviceCode').value.trim(),
                name: document.getElementById('serviceName').value.trim(),
                is_active: document.getElementById('serviceActive').checked
            };

            console.log('Сохранение сервиса:', serviceData);

            let result;
            if (this.editingServiceId) {
                // Обновляем существующий сервис
                result = await apiClient.put(
                    AppConstants.API_ENDPOINTS.SERVICE_BY_ID(this.editingServiceId),
                    serviceData
                );
                uiManager.showSuccess('Сервис успешно обновлен');
            } else {
                // Создаем новый сервис
                result = await apiClient.post(AppConstants.API_ENDPOINTS.SERVICES, serviceData);
                uiManager.showSuccess('Сервис успешно создан');
            }

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('serviceModal'));
            modal.hide();

            // Перезагружаем список сервисов через пагинацию
            if (window.adminPanel && window.adminPanel.servicesPagination) {
                await window.adminPanel.servicesPagination.loadData();
            }

        } catch (error) {
            console.error('Ошибка сохранения сервиса:', error);
            uiManager.showError(`Ошибка сохранения сервиса: ${error.message}`);
        }
    }

    /**
     * Редактирование сервиса
     * @param {number} serviceId - ID сервиса
     */
    editService(serviceId) {
        this.showServiceModal(serviceId);
    }

    /**
     * Удаление сервиса
     * @param {number} serviceId - ID сервиса
     */
    async deleteService(serviceId) {
        try {
            // Получаем данные сервиса для отображения в подтверждении
            let service = null;
            
            if (window.adminPanel && window.adminPanel.servicesPagination) {
                const currentData = window.adminPanel.servicesPagination.currentData;
                if (currentData) {
                    service = currentData.find(s => s.id === serviceId);
                }
            }
            
            if (!service) {
                service = await apiClient.get(AppConstants.API_ENDPOINTS.SERVICE_BY_ID(serviceId));
            }

            if (!service) {
                uiManager.showError('Сервис не найден');
                return;
            }

            const confirmed = await new Promise((resolve) => {
                uiManager.showConfirmModal(
                    'Подтверждение удаления',
                    `Вы уверены, что хотите удалить сервис "${service.name}" (${service.code})?<br><br>
                    <strong>Внимание:</strong> Удаление возможно только если у сервиса нет связанных цен или активаций.`,
                    () => resolve(true),
                    () => resolve(false)
                );
            });

            if (!confirmed) return;

            await apiClient.delete(AppConstants.API_ENDPOINTS.SERVICE_BY_ID(serviceId));
            uiManager.showSuccess('Сервис успешно удален');
            
            // Перезагружаем список сервисов через пагинацию
            if (window.adminPanel && window.adminPanel.servicesPagination) {
                await window.adminPanel.servicesPagination.loadData();
            }

        } catch (error) {
            console.error('Ошибка удаления сервиса:', error);
            uiManager.showError(`Ошибка удаления сервиса: ${error.message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля сервисов
window.servicesModule = new ServicesModule();
