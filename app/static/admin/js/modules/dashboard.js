/**
 * Модуль дашборда
 * Управляет отображением статистики и основной информации
 */
class DashboardModule {
    constructor() {
        this.autoRefreshInterval = null;
        this.isLoading = false;
    }

    /**
     * Инициализация модуля дашборда
     */
    init() {
        console.log('Инициализация модуля дашборда');
        this.setupAutoRefresh();
    }

    /**
     * Загрузка данных дашборда
     */
    async loadDashboard() {
        if (this.isLoading) return;
        
        console.log('Загрузка дашборда');
        this.isLoading = true;

        try {
            // Показываем индикатор загрузки
            this.showLoadingIndicator();

            // Загружаем основную статистику
            const stats = await apiClient.get(AppConstants.API_ENDPOINTS.STATISTICS);
            console.log('Статистика получена:', stats);

            // Обновляем основные карточки статистики
            this.updateMainStats(stats);

            // Загружаем дополнительные данные
            await this.loadAdditionalData(stats);

        } catch (error) {
            console.error('Ошибка загрузки дашборда:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Обновление основных статистических карточек
     * @param {Object} stats - Объект со статистикой
     */
    updateMainStats(stats) {
        console.log('Обновление основных статистических карточек');

        // Обновляем значения в карточках
        this.updateStatCard('total-users', stats.total_users || 0);
        this.updateStatCard('active-users', stats.active_users || 0);
        this.updateStatCard('total-activations', stats.total_activations || 0);
        this.updateStatCard('activations-today', stats.activations_today || 0);
        this.updateStatCard('total-revenue', DataFormatters.formatMoney(stats.total_revenue || 0));
        this.updateStatCard('total-balance', stats.total_balance || 0);
        this.updateStatCard('active-activations', stats.active_activations || 0);
        this.updateStatCard('success-rate', DataFormatters.formatPercent(stats.success_rate || 0));

        console.log('Основные статистические карточки обновлены');
    }

    /**
     * Обновление значения в статистической карточке
     * @param {string} elementId - ID элемента
     * @param {string|number} value - Новое значение
     */
    updateStatCard(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    /**
     * Загрузка дополнительных данных для дашборда
     * @param {Object} stats - Основная статистика
     */
    async loadAdditionalData(stats) {
        // Загружаем статистику провайдеров
        if (stats.provider_stats) {
            this.loadProviderStats(stats.provider_stats);
        }

        // Загружаем топ сервисов
        if (stats.top_services) {
            this.loadTopServices(stats.top_services);
        }

        // Загружаем дополнительные данные с небольшой задержкой
        setTimeout(() => this.loadRecentActivations(), 100);
        setTimeout(() => this.loadRecentUsers(), 200);
    }

    /**
     * Загрузка статистики провайдеров
     * @param {Array} providerStats - Массив статистики провайдеров
     */
    loadProviderStats(providerStats) {
        const container = document.getElementById('provider-stats');
        if (!container) return;

        console.log('Загрузка статистики провайдеров:', providerStats);

        // Убираем класс loading
        container.classList.remove('loading');

        if (!providerStats || providerStats.length === 0) {
            container.innerHTML = '<p class="text-muted">Нет данных о провайдерах</p>';
            return;
        }

        const html = providerStats.map(provider => `
            <div class="d-flex justify-content-between align-items-center mb-3 p-2 border-bottom">
                <div>
                    <strong>${DataFormatters.escapeHtml(provider.provider)}</strong>
                    <br>
                    <small class="text-muted">
                        Всего: ${provider.total} | Успешных: ${provider.successful}
                    </small>
                </div>
                <div class="text-end">
                    <span class="badge bg-${this.getSuccessRateColor(provider.success_rate)}">
                        ${DataFormatters.formatPercent(provider.success_rate)}
                    </span>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
        console.log('Статистика провайдеров загружена');
    }

    /**
     * Загрузка топ сервисов
     * @param {Array} topServices - Массив топ сервисов
     */
    loadTopServices(topServices) {
        const container = document.getElementById('top-services');
        if (!container) return;

        console.log('Загрузка топ сервисов:', topServices);

        // Убираем класс loading
        container.classList.remove('loading');

        if (!topServices || topServices.length === 0) {
            container.innerHTML = '<p class="text-muted">Нет данных о сервисах</p>';
            return;
        }

        const html = topServices.map((service, index) => `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                <div>
                    <span class="badge bg-primary me-2">${index + 1}</span>
                    <strong>${DataFormatters.escapeHtml(service.service)}</strong>
                </div>
                <div class="text-end">
                    <span class="badge bg-info">${DataFormatters.formatNumber(service.count)}</span>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
        console.log('Топ сервисов загружен');
    }

    /**
     * Загрузка последних активаций
     */
    async loadRecentActivations() {
        console.log('Загрузка последних активаций');
        
        try {
            const response = await apiClient.get(AppConstants.API_ENDPOINTS.ACTIVATIONS, { limit: 5 });
            const container = document.getElementById('recent-activations');
            
            if (!container) return;

            // Убираем класс loading
            container.classList.remove('loading');

            // Извлекаем данные из ответа API
            const activations = response.data || response;

            if (!activations || activations.length === 0) {
                container.innerHTML = '<p class="text-muted">Нет активаций</p>';
                return;
            }

            const html = activations.map(activation => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                    <div>
                        <strong>ID: ${activation.id}</strong>
                        <br>
                        <small class="text-muted">
                            ${activation.phone_number || 'Номер не назначен'}
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${getStatusColor(activation.status)}">
                            ${activation.status}
                        </span>
                        <br>
                        <small class="text-muted">
                            ${DataFormatters.formatDate(activation.ordered_at)}
                        </small>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
            console.log('Последние активации загружены');

        } catch (error) {
            console.error('Ошибка загрузки активаций:', error);
            const container = document.getElementById('recent-activations');
            if (container) {
                container.classList.remove('loading');
                container.innerHTML = '<p class="text-muted">Ошибка загрузки активаций</p>';
            }
        }
    }

    /**
     * Загрузка новых пользователей
     */
    async loadRecentUsers() {
        console.log('Загрузка новых пользователей');
        
        try {
            const users = await apiClient.get(AppConstants.API_ENDPOINTS.USERS, { limit: 5 });
            const container = document.getElementById('recent-users');
            
            if (!container) return;

            // Убираем класс loading
            container.classList.remove('loading');

            if (!users || users.length === 0) {
                container.innerHTML = '<p class="text-muted">Нет пользователей</p>';
                return;
            }

            const html = users.map(user => `
                <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                    <div>
                        <strong>${DataFormatters.escapeHtml(user.username)}</strong>
                        <br>
                        <small class="text-muted">${user.email || 'Нет email'}</small>
                        ${user.last_login ? 
                            `<br><small class="text-success">Последний вход: ${DataFormatters.formatDate(user.last_login)}</small>` : 
                            '<br><small class="text-warning">Не заходил</small>'
                        }
                    </div>
                    <div class="text-end">
                        <span class="badge bg-primary">
                            ${DataFormatters.formatMoney(user.balance)}
                        </span>
                        <br>
                        <small class="text-muted">
                            Создан: ${DataFormatters.formatDate(user.created_at)}
                        </small>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
            console.log('Новые пользователи загружены');

        } catch (error) {
            console.error('Ошибка загрузки пользователей:', error);
            const container = document.getElementById('recent-users');
            if (container) {
                container.classList.remove('loading');
                container.innerHTML = '<p class="text-muted">Ошибка загрузки пользователей</p>';
            }
        }
    }

    /**
     * Показать индикатор загрузки
     */
    showLoadingIndicator() {
        const dashboardContent = document.getElementById('dashboard-content');
        if (dashboardContent) {
            uiManager.showLoadingInContainer('dashboard-content', 'Загрузка статистики...');
        }
    }

    /**
     * Обработка ошибки загрузки
     * @param {Error} error - Объект ошибки
     */
    handleLoadError(error) {
        if (!error.message.includes('Не авторизован')) {
            uiManager.showError('Ошибка загрузки статистики: ' + error.message);

            // Устанавливаем сообщения об ошибке в блоки, если они не загрузились
            this.setErrorInContainer('provider-stats');
            this.setErrorInContainer('top-services');
            this.setErrorInContainer('recent-activations');
            this.setErrorInContainer('recent-users');
        }
    }

    /**
     * Установить сообщение об ошибке в контейнер
     * @param {string} containerId - ID контейнера
     */
    setErrorInContainer(containerId) {
        const container = document.getElementById(containerId);
        if (container && container.classList.contains('loading')) {
            container.classList.remove('loading');
            container.innerHTML = '<p class="text-muted">Ошибка загрузки данных</p>';
        }
    }

    /**
     * Получить цвет для процента успешности
     * @param {number} successRate - Процент успешности
     * @returns {string} CSS класс цвета
     */
    getSuccessRateColor(successRate) {
        if (successRate >= 80) return 'success';
        if (successRate >= 60) return 'warning';
        return 'danger';
    }

    /**
     * Настройка автообновления дашборда
     */
    setupAutoRefresh() {
        if (AppConstants.AUTO_REFRESH.ENABLED) {
            this.autoRefreshInterval = setInterval(() => {
                // Обновляем только если находимся на дашборде и авторизованы
                if (window.adminPanel && window.adminPanel.currentSection === 'dashboard' && authManager.isLoggedIn()) {
                    this.loadDashboard();
                }
            }, AppConstants.AUTO_REFRESH.DASHBOARD_INTERVAL);
        }
    }

    /**
     * Остановка автообновления
     */
    stopAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
        }
    }

    /**
     * Очистка ресурсов модуля
     */
    destroy() {
        this.stopAutoRefresh();
        this.isLoading = false;
    }
}

// Создаем глобальный экземпляр модуля дашборда
window.dashboardModule = new DashboardModule();
