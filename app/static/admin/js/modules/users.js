/**
 * Модуль управления пользователями
 * Обеспечивает функциональность для работы с пользователями
 */
class UsersModule {
    constructor() {
        this.currentUser = null;
        this.isEditing = false;
    }

    /**
     * Инициализация модуля пользователей
     */
    init() {
        console.log('Инициализация модуля пользователей');
    }

    /**
     * Загрузка списка пользователей
     */
    async loadUsers() {
        console.log('Загрузка списка пользователей');
        
        try {
            const users = await apiClient.get(AppConstants.API_ENDPOINTS.USERS);
            this.renderUsersList(users);
        } catch (error) {
            console.error('Ошибка загрузки пользователей:', error);
            uiManager.showError('Ошибка загрузки пользователей: ' + error.message);
        }
    }

    /**
     * Отображение списка пользователей
     * @param {Array} users - Массив пользователей
     */
    renderUsersList(users) {
        const container = document.getElementById('users-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-people"></i> Список пользователей</h5>
                    <button class="btn btn-primary" onclick="usersModule.showUserModal()">
                        <i class="bi bi-plus"></i> Добавить пользователя
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Имя пользователя</th>
                                    <th>Email</th>
                                    <th>Баланс</th>
                                    <th>Роль</th>
                                    <th>Последний вход</th>
                                    <th>Дата создания</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${users.map(user => this.renderUserRow(user)).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * Отображение строки пользователя в таблице
     * @param {Object} user - Объект пользователя
     * @returns {string} HTML строки таблицы
     */
    renderUserRow(user) {
        const roleInfo = DataFormatters.formatUserRole(user.role);
        
        return `
            <tr>
                <td>${user.id}</td>
                <td>${DataFormatters.escapeHtml(user.username)}</td>
                <td>${user.email || '-'}</td>
                <td>
                    ${DataFormatters.formatMoney(user.balance)}
                    ${user.reserved_balance > 0 ? 
                        `<br><small class="text-warning">(зарезервировано: ${DataFormatters.formatMoney(user.reserved_balance)})</small>` : 
                        ''
                    }
                </td>
                <td>
                    <span class="badge bg-${roleInfo.color}">${roleInfo.text}</span>
                </td>
                <td>
                    ${user.last_login ? 
                        `<span class="text-success">${DataFormatters.formatDate(user.last_login)}</span>` : 
                        '<span class="text-warning">Не заходил</span>'
                    }
                </td>
                <td>${DataFormatters.formatDate(user.created_at)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" 
                                onclick="usersModule.editUser(${user.id})" 
                                title="Редактировать">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" 
                                onclick="usersModule.showTopupModal(${user.id})" 
                                title="Пополнить баланс">
                            <i class="bi bi-plus-circle"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" 
                                onclick="usersModule.showDeductModal(${user.id})" 
                                title="Списать с баланса">
                            <i class="bi bi-dash-circle"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" 
                                onclick="usersModule.showApiKey(${user.id})" 
                                title="Показать API ключ">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" 
                                onclick="usersModule.regenerateApiKey(${user.id})" 
                                title="Обновить API ключ">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Показать модальное окно для создания/редактирования пользователя
     * @param {number|null} userId - ID пользователя для редактирования
     */
    showUserModal(userId = null) {
        this.isEditing = userId !== null;
        const title = this.isEditing ? 'Редактировать пользователя' : 'Добавить пользователя';

        const modalHtml = `
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <div class="mb-3">
                                    <label class="form-label">Имя пользователя *</label>
                                    <input type="text" class="form-control" name="username" required
                                           minlength="${AppConstants.VALIDATION.USERNAME_MIN_LENGTH}"
                                           maxlength="${AppConstants.VALIDATION.USERNAME_MAX_LENGTH}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" name="email">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Пароль ${this.isEditing ? '(оставьте пустым для сохранения текущего)' : '*'}</label>
                                    <input type="password" class="form-control" name="password" 
                                           ${this.isEditing ? '' : 'required'}
                                           minlength="${AppConstants.VALIDATION.PASSWORD_MIN_LENGTH}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Баланс</label>
                                    <input type="number" class="form-control" name="balance" 
                                           step="0.01" min="0" value="0">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Роль</label>
                                    <select class="form-select" name="role">
                                        <option value="${AppConstants.USER_ROLES.USER}">Пользователь</option>
                                        <option value="${AppConstants.USER_ROLES.ADMIN}">Администратор</option>
                                        <option value="${AppConstants.USER_ROLES.MODERATOR}">Модератор</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="usersModule.saveUser()">
                                ${this.isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('userModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные пользователя
        if (this.isEditing) {
            this.loadUserData(userId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();
    }

    /**
     * Загрузить данные пользователя для редактирования
     * @param {number} userId - ID пользователя
     */
    async loadUserData(userId) {
        try {
            const user = await apiClient.get(AppConstants.API_ENDPOINTS.USER_BY_ID(userId));
            this.currentUser = user;

            // Заполняем форму данными пользователя
            const form = document.getElementById('userForm');
            if (form) {
                form.username.value = user.username || '';
                form.email.value = user.email || '';
                form.balance.value = user.balance || 0;
                form.role.value = user.role || AppConstants.USER_ROLES.USER;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных пользователя:', error);
            uiManager.showError('Ошибка загрузки данных пользователя');
        }
    }

    /**
     * Сохранение пользователя
     */
    async saveUser() {
        const form = document.getElementById('userForm');
        if (!form) return;

        try {
            // Собираем данные формы
            const formData = new FormData(form);
            const userData = Object.fromEntries(formData);

            // Валидация
            if (!this.validateUserData(userData)) {
                return;
            }

            // Преобразуем баланс в число
            userData.balance = parseFloat(userData.balance) || 0;

            // Если редактируем и пароль пустой, удаляем его из данных
            if (this.isEditing && !userData.password) {
                delete userData.password;
            }

            let response;
            if (this.isEditing) {
                response = await apiClient.put(AppConstants.API_ENDPOINTS.USER_BY_ID(this.currentUser.id), userData);
            } else {
                response = await apiClient.post(AppConstants.API_ENDPOINTS.USERS, userData);
            }

            uiManager.showSuccess(
                this.isEditing ? 
                AppConstants.SUCCESS_MESSAGES.USER_UPDATED : 
                AppConstants.SUCCESS_MESSAGES.USER_CREATED
            );

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();

            // Перезагружаем список пользователей
            this.loadUsers();

        } catch (error) {
            console.error('Ошибка сохранения пользователя:', error);
            uiManager.showError('Ошибка сохранения пользователя: ' + error.message);
        }
    }

    /**
     * Валидация данных пользователя
     * @param {Object} userData - Данные пользователя
     * @returns {boolean} true если данные валидны
     */
    validateUserData(userData) {
        // Проверка имени пользователя
        if (!userData.username || userData.username.length < AppConstants.VALIDATION.USERNAME_MIN_LENGTH) {
            uiManager.showError(`Имя пользователя должно содержать минимум ${AppConstants.VALIDATION.USERNAME_MIN_LENGTH} символа`);
            return false;
        }

        // Проверка email если указан
        if (userData.email && !AppConstants.isValidEmail(userData.email)) {
            uiManager.showError('Некорректный формат email');
            return false;
        }

        // Проверка пароля для нового пользователя
        if (!this.isEditing && (!userData.password || userData.password.length < AppConstants.VALIDATION.PASSWORD_MIN_LENGTH)) {
            uiManager.showError(`Пароль должен содержать минимум ${AppConstants.VALIDATION.PASSWORD_MIN_LENGTH} символов`);
            return false;
        }

        return true;
    }

    /**
     * Редактирование пользователя
     * @param {number} userId - ID пользователя
     */
    editUser(userId) {
        this.showUserModal(userId);
    }

    /**
     * Показать модальное окно пополнения баланса
     * @param {number} userId - ID пользователя
     */
    showTopupModal(userId) {
        this.showBalanceModal(userId, 'topup');
    }

    /**
     * Показать модальное окно списания с баланса
     * @param {number} userId - ID пользователя
     */
    showDeductModal(userId) {
        this.showBalanceModal(userId, 'deduct');
    }

    /**
     * Показать модальное окно изменения баланса
     * @param {number} userId - ID пользователя
     * @param {string} type - Тип операции (topup/deduct)
     */
    showBalanceModal(userId, type) {
        const isTopup = type === 'topup';
        const title = isTopup ? 'Пополнить баланс' : 'Списать с баланса';
        const buttonText = isTopup ? 'Пополнить' : 'Списать';
        const buttonClass = isTopup ? 'btn-success' : 'btn-warning';

        const modalHtml = `
            <div class="modal fade" id="balanceModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="balanceForm">
                                <div class="mb-3">
                                    <label class="form-label">Сумма *</label>
                                    <input type="number" class="form-control" name="amount" 
                                           step="0.01" min="0.01" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Комментарий</label>
                                    <textarea class="form-control" name="comment" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn ${buttonClass}" 
                                    onclick="usersModule.updateBalance(${userId}, '${type}')">
                                ${buttonText}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('balanceModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('balanceModal'));
        modal.show();
    }

    /**
     * Обновление баланса пользователя
     * @param {number} userId - ID пользователя
     * @param {string} type - Тип операции (topup/deduct)
     */
    async updateBalance(userId, type) {
        const form = document.getElementById('balanceForm');
        if (!form) return;

        try {
            const formData = new FormData(form);
            const amount = parseFloat(formData.get('amount'));
            const comment = formData.get('comment');

            if (!amount || amount <= 0) {
                uiManager.showError('Введите корректную сумму');
                return;
            }

            const endpoint = type === 'topup' ? 
                AppConstants.API_ENDPOINTS.USER_TOPUP(userId) : 
                AppConstants.API_ENDPOINTS.USER_DEDUCT(userId);

            await apiClient.post(endpoint, { amount, comment });

            uiManager.showSuccess(AppConstants.SUCCESS_MESSAGES.BALANCE_UPDATED);

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('balanceModal'));
            modal.hide();

            // Перезагружаем список пользователей
            this.loadUsers();

        } catch (error) {
            console.error('Ошибка обновления баланса:', error);
            uiManager.showError('Ошибка обновления баланса: ' + error.message);
        }
    }

    /**
     * Показать API ключ пользователя
     * @param {number} userId - ID пользователя
     */
    async showApiKey(userId) {
        try {
            const response = await apiClient.get(AppConstants.API_ENDPOINTS.USER_API_KEY(userId));
            
            uiManager.showConfirmModal(
                'API ключ пользователя',
                `<div class="alert alert-info">
                    <strong>API ключ:</strong><br>
                    <code style="word-break: break-all;">${response.api_key}</code>
                </div>`,
                () => {
                    // Копируем в буфер обмена
                    navigator.clipboard.writeText(response.api_key).then(() => {
                        uiManager.showSuccess('API ключ скопирован в буфер обмена');
                    });
                }
            );
        } catch (error) {
            console.error('Ошибка получения API ключа:', error);
            uiManager.showError('Ошибка получения API ключа: ' + error.message);
        }
    }

    /**
     * Обновить API ключ пользователя
     * @param {number} userId - ID пользователя
     */
    regenerateApiKey(userId) {
        uiManager.showConfirmModal(
            'Обновить API ключ',
            AppConstants.CONFIRM_MESSAGES.REGENERATE_API_KEY,
            async () => {
                try {
                    const response = await apiClient.post(AppConstants.API_ENDPOINTS.USER_REGENERATE_API_KEY(userId));
                    
                    uiManager.showSuccess(AppConstants.SUCCESS_MESSAGES.API_KEY_REGENERATED);
                    
                    // Показываем новый ключ
                    uiManager.showConfirmModal(
                        'Новый API ключ',
                        `<div class="alert alert-success">
                            <strong>Новый API ключ:</strong><br>
                            <code style="word-break: break-all;">${response.api_key}</code>
                        </div>`,
                        () => {
                            navigator.clipboard.writeText(response.api_key).then(() => {
                                uiManager.showSuccess('API ключ скопирован в буфер обмена');
                            });
                        }
                    );
                } catch (error) {
                    console.error('Ошибка обновления API ключа:', error);
                    uiManager.showError('Ошибка обновления API ключа: ' + error.message);
                }
            }
        );
    }
}

// Создаем глобальный экземпляр модуля пользователей
window.usersModule = new UsersModule();
