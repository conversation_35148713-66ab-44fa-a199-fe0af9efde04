/**
 * Модуль управления провайдерами
 * Обеспечивает функциональность для работы с SMS провайдерами в админ панели
 */
class ProvidersModule {
    constructor() {
        this.isLoading = false;
        this.currentProviders = [];
        this.editingProviderId = null;
    }

    /**
     * Инициализация модуля провайдеров
     */
    init() {
        console.log('Инициализация модуля провайдеров');
    }

    /**
     * Загрузка списка провайдеров
     */
    async loadProviders() {
        if (this.isLoading) return;
        
        console.log('Загрузка провайдеров');
        this.isLoading = true;

        try {
            // Показываем индикатор загрузки
            this.showLoadingIndicator();

            // Загружаем провайдеров через API
            const response = await apiClient.get(AppConstants.API_ENDPOINTS.PROVIDERS);
            console.log('Провайдеры получены:', response);

            // API возвращает объект с массивом providers
            const providers = response.providers || [];
            this.currentProviders = providers;
            this.renderProviders(providers);

        } catch (error) {
            console.error('Ошибка загрузки провайдеров:', error);
            this.handleLoadError(error);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Отображение индикатора загрузки
     */
    showLoadingIndicator() {
        const container = document.getElementById('providers-content');
        if (container) {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <span class="ms-2">Загрузка провайдеров...</span>
                </div>
            `;
        }
    }

    /**
     * Обработка ошибки загрузки
     * @param {Error} error - Ошибка загрузки
     */
    handleLoadError(error) {
        const container = document.getElementById('providers-content');
        if (container) {
            let errorMessage = 'Ошибка загрузки данных';
            
            if (error.message.includes('Не авторизован')) {
                errorMessage = 'Ошибка авторизации. Пожалуйста, войдите в систему заново.';
            } else if (error.message.includes('Failed to fetch')) {
                errorMessage = 'Ошибка соединения с сервером';
            }

            container.innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Ошибка загрузки провайдеров:</strong> ${errorMessage}
                    <button class="btn btn-outline-danger btn-sm ms-2" onclick="providersModule.loadProviders()">
                        <i class="bi bi-arrow-clockwise"></i> Повторить
                    </button>
                </div>
            `;
        }

        // Показываем уведомление об ошибке
        if (window.uiManager) {
            uiManager.showError(`Ошибка загрузки провайдеров: ${error.message}`);
        }
    }

    /**
     * Отображение списка провайдеров
     * @param {Array} providers - Массив провайдеров
     */
    renderProviders(providers) {
        const container = document.getElementById('providers-content');
        if (!container) return;

        const html = `
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="bi bi-cloud"></i> SMS Провайдеры</h5>
                    <div>
                        <button class="btn btn-outline-secondary me-2" onclick="providersModule.loadProviders()" title="Обновить список">
                            <i class="bi bi-arrow-clockwise"></i> Обновить
                        </button>
                        <button class="btn btn-outline-primary me-2" onclick="providersModule.testAllProviders()">
                            <i class="bi bi-check-circle"></i> Тест всех
                        </button>
                        <button class="btn btn-primary" onclick="providersModule.showProviderModal()">
                            <i class="bi bi-plus"></i> Добавить провайдера
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Название</th>
                                    <th>API URL</th>
                                    <th>Формат API</th>
                                    <th>Приоритет</th>
                                    <th>Статус</th>
                                    <th>Действия</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${providers.map(provider => `
                                    <tr>
                                        <td>${provider.id}</td>
                                        <td>
                                            <strong>${escapeHtml(provider.name)}</strong>
                                            ${provider.description ? `<br><small class="text-muted">${escapeHtml(provider.description)}</small>` : ''}
                                        </td>
                                        <td>
                                            <code>${escapeHtml(provider.api_url)}</code>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">${escapeHtml(provider.api_format)}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">${provider.priority}</span>
                                        </td>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" 
                                                       id="provider-${provider.id}" 
                                                       ${provider.is_active ? 'checked' : ''}
                                                       onchange="providersModule.toggleProvider(${provider.id}, this.checked)">
                                                <label class="form-check-label" for="provider-${provider.id}">
                                                    <span class="badge bg-${getBooleanColor(provider.is_active)}">
                                                        ${formatBoolean(provider.is_active)}
                                                    </span>
                                                </label>
                                            </div>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-success me-1" 
                                                    onclick="providersModule.testProvider(${provider.id})" 
                                                    title="Тестировать">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-primary me-1" 
                                                    onclick="providersModule.editProvider(${provider.id})" 
                                                    title="Редактировать">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" 
                                                    onclick="providersModule.deleteProvider(${provider.id})" 
                                                    title="Удалить">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    ${providers.length === 0 ? `
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-cloud" style="font-size: 3rem;"></i>
                            <p class="mt-2">Провайдеры не найдены</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * Показать модальное окно для создания/редактирования провайдера
     * @param {number|null} providerId - ID провайдера для редактирования (null для создания)
     */
    showProviderModal(providerId = null) {
        this.editingProviderId = providerId;
        const isEditing = providerId !== null;
        
        const modalHtml = `
            <div class="modal fade" id="providerModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-cloud"></i>
                                ${isEditing ? 'Редактировать провайдера' : 'Добавить провайдера'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="providerForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerName" class="form-label">Название *</label>
                                            <input type="text" class="form-control" id="providerName"
                                                   placeholder="Например: SMSLive" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerDisplayName" class="form-label">Отображаемое название *</label>
                                            <input type="text" class="form-control" id="providerDisplayName"
                                                   placeholder="Например: SMS Live Provider" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerApiFormat" class="form-label">Формат API *</label>
                                            <select class="form-select" id="providerApiFormat" required>
                                                <option value="">Выберите формат</option>
                                                <option value="smsactivate">SMS Activate</option>
                                                <option value="firefox">Firefox</option>
                                                <option value="smslive">SMS Live</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="providerApiUrl" class="form-label">API URL *</label>
                                    <input type="url" class="form-control" id="providerApiUrl" 
                                           placeholder="https://api.example.com" required>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerApiKey" class="form-label">API Ключ</label>
                                            <input type="text" class="form-control" id="providerApiKey" 
                                                   placeholder="API ключ провайдера">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="providerPriority" class="form-label">Приоритет</label>
                                            <input type="number" class="form-control" id="providerPriority" 
                                                   value="1" min="1" max="100">
                                            <div class="form-text">Чем меньше число, тем выше приоритет</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="providerDescription" class="form-label">Описание</label>
                                    <textarea class="form-control" id="providerDescription" rows="3" 
                                              placeholder="Описание провайдера"></textarea>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="providerActive" checked>
                                        <label class="form-check-label" for="providerActive">
                                            Активен
                                        </label>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                            <button type="button" class="btn btn-primary" onclick="providersModule.saveProvider()">
                                ${isEditing ? 'Сохранить' : 'Создать'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('providerModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Если редактируем, загружаем данные провайдера
        if (isEditing) {
            this.loadProviderData(providerId);
        }

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('providerModal'));
        modal.show();
    }

    /**
     * Загрузка данных провайдера для редактирования
     * @param {number} providerId - ID провайдера
     */
    async loadProviderData(providerId) {
        try {
            const provider = this.currentProviders.find(p => p.id === providerId);
            if (provider) {
                document.getElementById('providerName').value = provider.name;
                document.getElementById('providerDisplayName').value = provider.display_name || provider.name;
                document.getElementById('providerApiFormat').value = provider.api_format;
                document.getElementById('providerApiUrl').value = provider.api_url;
                document.getElementById('providerApiKey').value = provider.api_key || '';
                document.getElementById('providerPriority').value = provider.priority;
                document.getElementById('providerDescription').value = provider.settings?.description || '';
                document.getElementById('providerActive').checked = provider.is_active;
            }
        } catch (error) {
            console.error('Ошибка загрузки данных провайдера:', error);
            uiManager.showError('Ошибка загрузки данных провайдера');
        }
    }

    /**
     * Сохранение провайдера (создание или обновление)
     */
    async saveProvider() {
        try {
            const form = document.getElementById('providerForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const providerData = {
                name: document.getElementById('providerName').value.trim(),
                display_name: document.getElementById('providerDisplayName').value.trim(),
                api_format: document.getElementById('providerApiFormat').value,
                api_url: document.getElementById('providerApiUrl').value.trim(),
                api_key: document.getElementById('providerApiKey').value.trim() || null,
                priority: parseInt(document.getElementById('providerPriority').value),
                is_active: document.getElementById('providerActive').checked,
                settings: {
                    description: document.getElementById('providerDescription').value.trim() || null
                }
            };

            console.log('Сохранение провайдера:', providerData);

            let result;
            if (this.editingProviderId) {
                // Обновляем существующего провайдера
                result = await apiClient.put(
                    AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(this.editingProviderId),
                    providerData
                );
                uiManager.showSuccess('Провайдер успешно обновлен');
            } else {
                // Создаем нового провайдера
                result = await apiClient.post(AppConstants.API_ENDPOINTS.PROVIDERS, providerData);
                uiManager.showSuccess('Провайдер успешно создан');
            }

            // Закрываем модальное окно
            const modal = bootstrap.Modal.getInstance(document.getElementById('providerModal'));
            modal.hide();

            // Перезагружаем список провайдеров
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка сохранения провайдера:', error);
            uiManager.showError(`Ошибка сохранения провайдера: ${error.message}`);
        }
    }

    /**
     * Переключение статуса провайдера
     * @param {number} providerId - ID провайдера
     * @param {boolean} isActive - Новый статус
     */
    async toggleProvider(providerId, isActive) {
        try {
            await apiClient.patch(
                `${AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(providerId)}/toggle`,
                { is_active: isActive }
            );
            
            uiManager.showSuccess(`Провайдер ${isActive ? 'активирован' : 'деактивирован'}`);
            
            // Обновляем локальные данные
            const provider = this.currentProviders.find(p => p.id === providerId);
            if (provider) {
                provider.is_active = isActive;
            }

        } catch (error) {
            console.error('Ошибка переключения провайдера:', error);
            uiManager.showError(`Ошибка переключения провайдера: ${error.message}`);
            
            // Возвращаем чекбокс в исходное состояние
            const checkbox = document.getElementById(`provider-${providerId}`);
            if (checkbox) {
                checkbox.checked = !isActive;
            }
        }
    }

    /**
     * Тестирование провайдера
     * @param {number} providerId - ID провайдера
     */
    async testProvider(providerId) {
        try {
            uiManager.showInfo('Тестирование провайдера...');
            
            const result = await apiClient.post(
                `${AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(providerId)}/test`
            );
            
            if (result.success) {
                uiManager.showSuccess('Провайдер работает корректно');
            } else {
                uiManager.showError(`Ошибка тестирования: ${result.error || 'Неизвестная ошибка'}`);
            }

        } catch (error) {
            console.error('Ошибка тестирования провайдера:', error);
            uiManager.showError(`Ошибка тестирования провайдера: ${error.message}`);
        }
    }

    /**
     * Тестирование всех провайдеров
     */
    async testAllProviders() {
        try {
            uiManager.showInfo('Тестирование всех провайдеров...');

            const results = await apiClient.post(`${AppConstants.API_ENDPOINTS.PROVIDERS}/test-all`);

            // Показываем результаты в модальном окне
            this.showTestResults(results.results || results);

        } catch (error) {
            console.error('Ошибка тестирования провайдеров:', error);
            uiManager.showError(`Ошибка тестирования провайдеров: ${error.message}`);
        }
    }

    /**
     * Показать результаты тестирования
     * @param {Array} results - Результаты тестирования
     */
    showTestResults(results) {
        const modalHtml = `
            <div class="modal fade" id="testResultsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="bi bi-check-circle"></i>
                                Результаты тестирования провайдеров
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Провайдер</th>
                                            <th>Статус</th>
                                            <th>Время ответа</th>
                                            <th>Сообщение</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${results.map(result => `
                                            <tr>
                                                <td><strong>${escapeHtml(result.provider_name)}</strong></td>
                                                <td>
                                                    <span class="badge bg-${result.success ? 'success' : 'danger'}">
                                                        ${result.success ? 'Успех' : 'Ошибка'}
                                                    </span>
                                                </td>
                                                <td>${result.response_time ? `${result.response_time}ms` : '-'}</td>
                                                <td>${escapeHtml(result.message || '-')}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Удаляем предыдущее модальное окно если есть
        const existingModal = document.getElementById('testResultsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Добавляем новое модальное окно
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Показываем модальное окно
        const modal = new bootstrap.Modal(document.getElementById('testResultsModal'));
        modal.show();
    }

    /**
     * Редактирование провайдера
     * @param {number} providerId - ID провайдера
     */
    editProvider(providerId) {
        this.showProviderModal(providerId);
    }

    /**
     * Удаление провайдера
     * @param {number} providerId - ID провайдера
     */
    async deleteProvider(providerId) {
        const provider = this.currentProviders.find(p => p.id === providerId);
        if (!provider) return;

        const confirmed = await new Promise((resolve) => {
            uiManager.showConfirmModal(
                'Подтверждение удаления',
                `Вы уверены, что хотите удалить провайдера "${provider.name}"?<br><br>
                <strong>Внимание:</strong> Это действие нельзя отменить.`,
                () => resolve(true),
                () => resolve(false)
            );
        });

        if (!confirmed) return;

        try {
            await apiClient.delete(AppConstants.API_ENDPOINTS.PROVIDER_BY_ID(providerId));
            uiManager.showSuccess('Провайдер успешно удален');
            
            // Перезагружаем список провайдеров
            await this.loadProviders();

        } catch (error) {
            console.error('Ошибка удаления провайдера:', error);
            uiManager.showError(`Ошибка удаления провайдера: ${error.message}`);
        }
    }
}

// Создаем глобальный экземпляр модуля провайдеров
window.providersModule = new ProvidersModule();
