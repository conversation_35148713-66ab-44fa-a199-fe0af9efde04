/**
 * Админ панель SMS Proxy Service
 * Основной JavaScript файл для управления интерфейсом
 */

// УДАЛЕНО: Класс PaginationManager перенесен в /core/pagination.js

class AdminPanel {
    constructor() {
        this.apiKey = localStorage.getItem('admin_api_key') || '';
        console.log('AdminPanel constructor - API key from localStorage:', this.apiKey ? `Present (${this.apiKey.substring(0, 10)}...)` : 'Missing');
        this.currentSection = 'dashboard';
        this.pendingRequest = null; // Для сохранения прерванного запроса

        // Инициализация менеджеров пагинации для разных разделов
        this.initializePaginationManagers();

        this.init();
    }

    /**
     * Инициализация менеджеров пагинации для всех разделов
     */
    initializePaginationManagers() {
        // Менеджер пагинации для активаций
        this.activationsPagination = new PaginationManager(
            'activations-content',
            async (params) => {
                const activations = await this.apiRequest(
                    `/api/admin/activations?skip=${params.skip}&limit=${params.limit}`
                );
                return activations;
            },
            {
                pageSize: 20,
                pageSizeOptions: [20, 50, 100, 200]
            }
        );

        // Переопределяем метод renderContent для активаций
        this.activationsPagination.renderContent = (activations) => {
            const container = document.getElementById('activations-content');
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-phone-vibrate"></i> Список активаций</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Пользователь</th>
                                        <th>Сервис</th>
                                        <th>Страна</th>
                                        <th>Номер телефона</th>
                                        <th>Статус</th>
                                        <th>SMS код</th>
                                        <th>Дата создания</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${activations.map(activation => `
                                        <tr>
                                            <td>${activation.id}</td>
                                            <td>${activation.user ? activation.user.username : activation.user_id}</td>
                                            <td>${activation.service ? activation.service.name : activation.service_id}</td>
                                            <td>${activation.country ? activation.country.name : activation.country_id}</td>
                                            <td>${activation.phone_number || '-'}</td>
                                            <td><span class="badge bg-${this.getStatusColor(activation.status)}">${activation.status}</span></td>
                                            <td>${activation.sms_code || '-'}</td>
                                            <td>${this.formatDate(activation.ordered_at)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            // Находим контейнер для контента (исключая пагинацию)
            let contentContainer = container.querySelector('.content-wrapper');
            if (!contentContainer) {
                contentContainer = document.createElement('div');
                contentContainer.className = 'content-wrapper';
                container.appendChild(contentContainer);
            }
            contentContainer.innerHTML = html;
        };

        // Менеджер пагинации для транзакций
        this.transactionsPagination = new PaginationManager(
            'transactions-content',
            async (params) => {
                const transactions = await this.apiRequest(
                    `/api/admin/transactions?skip=${params.skip}&limit=${params.limit}`
                );
                return transactions;
            },
            {
                pageSize: 20,
                pageSizeOptions: [20, 50, 100, 200]
            }
        );

        // Переопределяем метод renderContent для транзакций
        this.transactionsPagination.renderContent = (transactions) => {
            const container = document.getElementById('transactions-content');
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-credit-card"></i> История транзакций</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Пользователь</th>
                                        <th>Тип</th>
                                        <th>Сумма</th>
                                        <th>Описание</th>
                                        <th>Дата</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${transactions.map(tx => `
                                        <tr>
                                            <td>${tx.id}</td>
                                            <td>${tx.user ? tx.user.username : tx.user_id}</td>
                                            <td><span class="badge bg-${tx.type === 'TOPUP' ? 'success' : 'warning'}">${tx.type}</span></td>
                                            <td>$${tx.amount}</td>
                                            <td>${tx.comment || '-'}</td>
                                            <td>${this.formatDate(tx.timestamp)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            let contentContainer = container.querySelector('.content-wrapper');
            if (!contentContainer) {
                contentContainer = document.createElement('div');
                contentContainer.className = 'content-wrapper';
                container.appendChild(contentContainer);
            }
            contentContainer.innerHTML = html;
        };

        // Менеджер пагинации для сервисов
        this.servicesPagination = new PaginationManager(
            'services-content',
            async (params) => {
                const services = await this.apiRequest(
                    `/api/admin/services?skip=${params.skip}&limit=${params.limit}`
                );
                return services;
            },
            {
                pageSize: 20,
                pageSizeOptions: [20, 50, 100, 200]
            }
        );

        // Переопределяем метод renderContent для сервисов
        this.servicesPagination.renderContent = (services) => {
            const container = document.getElementById('services-content');
            const html = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-gear"></i> Список сервисов</h5>
                        <button class="btn btn-primary" onclick="adminPanel.showServiceModal()">
                            <i class="bi bi-plus"></i> Добавить сервис
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Код</th>
                                        <th>Название</th>
                                        <th>Активен</th>
                                        <th>Действия</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${services.map(service => `
                                        <tr>
                                            <td>${service.id}</td>
                                            <td><code>${service.code}</code></td>
                                            <td>${service.name}</td>
                                            <td><span class="badge bg-${service.is_active ? 'success' : 'secondary'}">${service.is_active ? 'Да' : 'Нет'}</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1" onclick="adminPanel.editService(${service.id})" title="Редактировать">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger" onclick="adminPanel.deleteService(${service.id})" title="Удалить">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            let contentContainer = container.querySelector('.content-wrapper');
            if (!contentContainer) {
                contentContainer = document.createElement('div');
                contentContainer.className = 'content-wrapper';
                container.appendChild(contentContainer);
            }
            contentContainer.innerHTML = html;
        };

        // Менеджер пагинации для логов
        this.logsPagination = new PaginationManager(
            'logs-content',
            async (params) => {
                const logs = await this.apiRequest(
                    `/api/admin/logs?skip=${params.skip}&limit=${params.limit}`
                );
                return logs;
            },
            {
                pageSize: 20,
                pageSizeOptions: [20, 50, 100, 200]
            }
        );

        // Переопределяем метод renderContent для логов
        this.logsPagination.renderContent = (logs) => {
            const container = document.getElementById('logs-content');
            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-journal-text"></i> Логи системы</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Пользователь</th>
                                        <th>Действие</th>
                                        <th>Детали</th>
                                        <th>IP адрес</th>
                                        <th>Дата</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${logs.map(log => `
                                        <tr>
                                            <td>${log.id}</td>
                                            <td>${log.user ? log.user.username : (log.user_id || '-')}</td>
                                            <td><span class="badge bg-info">${log.action}</span></td>
                                            <td>${log.details || '-'}</td>
                                            <td>${log.ip_address || '-'}</td>
                                            <td>${this.formatDate(log.timestamp)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            let contentContainer = container.querySelector('.content-wrapper');
            if (!contentContainer) {
                contentContainer = document.createElement('div');
                contentContainer.className = 'content-wrapper';
                container.appendChild(contentContainer);
            }
            contentContainer.innerHTML = html;
        };
    }

    /**
     * Инициализация панели
     */
    init() {
        console.log('AdminPanel init started');
        this.setupEventListeners();

        // Проверяем авторизацию - используем основную форму авторизации из HTML
        if (!this.apiKey) {
            console.log('No API key found, using main auth form');
            // Не показываем модальное окно, используем основную форму авторизации
        } else {
            console.log('API key found, loading dashboard');
            // Проверяем валидность API ключа при загрузке дашборда
            this.loadDashboard().catch(error => {
                if (error.message.includes('Не авторизован')) {
                    console.log('API key is invalid, clearing and using main auth form');
                    localStorage.removeItem('admin_api_key');
                    this.apiKey = '';
                    // Не показываем модальное окно, используем основную форму авторизации
                }
            });
        }
    }

    /**
     * Настройка обработчиков событий
     */
    setupEventListeners() {
        // Навигация по секциям
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.switchSection(section);

                // Закрываем мобильное меню после выбора секции
                this.closeMobileSidebar();
            });
        });

        // Мобильное меню
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMobileSidebar();
            });
        }

        // Закрытие меню при клике на overlay
        const mobileOverlay = document.getElementById('mobileOverlay');
        if (mobileOverlay) {
            mobileOverlay.addEventListener('click', () => {
                this.closeMobileSidebar();
            });
        }

        // Закрытие мобильного меню при клике вне его области
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');

            // Проверяем что клик был не по боковому меню и не по кнопке меню
            if (sidebar && sidebar.classList.contains('show') &&
                !sidebar.contains(e.target) &&
                !sidebarToggle.contains(e.target)) {
                this.closeMobileSidebar();
            }
        });

        // Закрытие мобильного меню при нажатии Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileSidebar();
            }
        });

        // Поддержка жестов смахивания для мобильных устройств
        this.setupTouchGestures();

        // Автообновление дашборда каждые 30 секунд
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboard();
            }
        }, 30000);
    }

    /**
     * Переключение состояния мобильного бокового меню
     */
    toggleMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar.classList.contains('show')) {
            this.closeMobileSidebar();
        } else {
            this.openMobileSidebar();
        }
    }

    /**
     * Открытие мобильного бокового меню
     */
    openMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('mobileOverlay');

        if (sidebar) {
            sidebar.classList.add('show');
        }

        if (overlay) {
            overlay.classList.add('show');
        }

        // Блокируем скролл страницы
        document.body.classList.add('sidebar-open');
    }

    /**
     * Закрытие мобильного бокового меню
     */
    closeMobileSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.getElementById('mobileOverlay');

        if (sidebar && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }

        if (overlay && overlay.classList.contains('show')) {
            overlay.classList.remove('show');
        }

        // Разрешаем скролл страницы
        document.body.classList.remove('sidebar-open');
    }

    /**
     * Настройка touch жестов для мобильных устройств
     */
    setupTouchGestures() {
        let touchStartX = 0;
        let touchStartY = 0;
        let touchEndX = 0;
        let touchEndY = 0;

        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');

        // Обработка начала касания
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });

        // Обработка окончания касания
        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = Math.abs(touchEndY - touchStartY);

            // Проверяем что это горизонтальный свайп
            if (Math.abs(deltaX) > deltaY && Math.abs(deltaX) > 50) {
                // Свайп влево - закрываем меню (если оно открыто)
                if (deltaX < -50 && sidebar.classList.contains('show')) {
                    this.closeMobileSidebar();
                }
                // Свайп вправо от левого края - открываем меню (если оно закрыто)
                else if (deltaX > 50 && touchStartX < 50 && !sidebar.classList.contains('show')) {
                    this.openMobileSidebar();
                }
            }
        }, { passive: true });

        // Обработка касания области контента для закрытия меню
        if (mainContent) {
            mainContent.addEventListener('touchstart', (e) => {
                if (sidebar.classList.contains('show')) {
                    // Если меню открыто и касание произошло в области контента,
                    // готовимся к возможному закрытию меню
                    e.preventDefault();
                }
            }, { passive: false });
        }
    }

    /**
     * Переключение между секциями
     */
    switchSection(section) {
        // Скрываем все секции
        document.querySelectorAll('.content-section').forEach(el => {
            el.style.display = 'none';
        });

        // Убираем активный класс с навигации
        document.querySelectorAll('.sidebar .nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Показываем нужную секцию
        const sectionElement = document.getElementById(`${section}-section`);
        if (sectionElement) {
            sectionElement.style.display = 'block';
        }

        // Добавляем активный класс
        const activeLink = document.querySelector(`[data-section="${section}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }

        this.currentSection = section;

        // Загружаем данные для секции
        this.loadSectionData(section);
    }

    /**
     * Загрузка данных для секции
     */
    async loadSectionData(section) {
        switch (section) {
            case 'dashboard':
                // Используем модуль дашборда
                if (window.dashboardModule) {
                    await dashboardModule.loadDashboard();
                }
                break;
            case 'users':
                // Используем модуль пользователей
                if (window.usersModule) {
                    await usersModule.loadUsers();
                }
                break;
            case 'activations':
                // Используем менеджер пагинации для активаций
                await this.activationsPagination.loadData();
                break;
            case 'transactions':
                // Используем менеджер пагинации для транзакций
                await this.transactionsPagination.loadData();
                break;
            case 'services':
                // Используем менеджер пагинации для сервисов
                await this.servicesPagination.loadData();
                break;
            case 'countries':
                await this.loadCountries();
                break;
            case 'prices':
                await this.loadPrices();
                break;
            case 'settings':
                await this.loadSettings();
                break;
            case 'logs':
                // Используем менеджер пагинации для логов
                await this.logsPagination.loadData();
                break;
            case 'providers':
                await this.loadProviders();
                break;
        }
    }

    // УДАЛЕНО: Функции дашборда перенесены в /modules/dashboard.js
    // - loadDashboard()
    // - loadProviderStats()
    // - loadTopServices()
    // - loadRecentActivations()
    // - loadRecentUsers()

    // УДАЛЕНО: Функция loadUsers перенесена в /modules/users.js

    /**
     * Загрузка стран (используем модуль стран)
     */
    async loadCountries() {
        if (window.countriesModule) {
            await countriesModule.loadCountries();
        } else {
            const container = document.getElementById('countries-content');
            if (container) {
                container.innerHTML = '<div class="alert alert-warning">Модуль стран не загружен</div>';
            }
        }
    }

    /**
     * Загрузка цен (используем модуль цен)
     */
    async loadPrices() {
        if (window.pricesModule) {
            await pricesModule.loadPrices();
        } else {
            const container = document.getElementById('prices-content');
            if (container) {
                container.innerHTML = '<div class="alert alert-warning">Модуль цен не загружен</div>';
            }
        }
    }

    /**
     * Загрузка провайдеров (используем модуль провайдеров)
     */
    async loadProviders() {
        if (window.providersModule) {
            await providersModule.loadProviders();
        } else {
            const container = document.getElementById('providers-content');
            if (container) {
                container.innerHTML = '<div class="alert alert-warning">Модуль провайдеров не загружен</div>';
            }
        }
    }

    /**
     * Показать модальное окно для создания/редактирования сервиса
     * @param {number|null} serviceId - ID сервиса для редактирования
     */
    showServiceModal(serviceId = null) {
        if (window.servicesModule) {
            servicesModule.showServiceModal(serviceId);
        } else {
            console.error('Модуль сервисов не загружен');
            this.showError('Модуль сервисов не загружен');
        }
    }

    /**
     * Редактирование сервиса
     * @param {number} serviceId - ID сервиса
     */
    editService(serviceId) {
        if (window.servicesModule) {
            servicesModule.editService(serviceId);
        } else {
            console.error('Модуль сервисов не загружен');
            this.showError('Модуль сервисов не загружен');
        }
    }

    /**
     * Удаление сервиса
     * @param {number} serviceId - ID сервиса
     */
    deleteService(serviceId) {
        if (window.servicesModule) {
            servicesModule.deleteService(serviceId);
        } else {
            console.error('Модуль сервисов не загружен');
            this.showError('Модуль сервисов не загружен');
        }
    }

    /**
     * API запрос (используем apiClient)
     */
    async apiRequest(url, options = {}) {
        if (window.apiClient) {
            return await apiClient.request(url, options);
        } else {
            throw new Error('API клиент не инициализирован');
        }
    }

    /**
     * Показать ошибку (используем uiManager)
     */
    showError(message) {
        if (window.uiManager) {
            uiManager.showError(message);
        } else {
            console.error('UI Manager не инициализирован:', message);
        }
    }

    /**
     * Показать успешное сообщение (используем uiManager)
     */
    showSuccess(message) {
        if (window.uiManager) {
            uiManager.showSuccess(message);
        } else {
            console.log('Успех:', message);
        }
    }

    /**
     * Выход из системы (используем authManager)
     */
    logout() {
        if (window.authManager) {
            authManager.logout();
        } else {
            console.error('Auth Manager не инициализирован');
        }
    }

    /**
     * Загрузка транзакций - УДАЛЕНО: теперь используется пагинация через transactionsPagination
     */

    // УДАЛЕНО: Функция loadServices - используется пагинация через servicesPagination

    // УДАЛЕНО: Функции loadCountries и loadPrices - можно создать отдельные модули

    /**
     * Загрузка настроек
     */
    async loadSettings() {
        try {
            const settings = await this.apiRequest('/api/admin/settings');
            const container = document.getElementById('settings-content');

            const html = `
                <div class="card">
                    <div class="card-header">
                        <h5>Настройки системы</h5>
                    </div>
                    <div class="card-body">
                        <form id="settingsForm">
                            <!-- Основные системные настройки -->
                            <h6 class="mb-3"><i class="bi bi-sliders"></i> Системные настройки</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Время ожидания SMS (сек)</label>
                                        <input type="number" class="form-control" name="sms_timeout" value="${settings?.sms_timeout || 1200}" min="60" max="3600">
                                        <div class="form-text">Время ожидания SMS кода в секундах (60-3600 сек)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Режим отладки</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="debug_mode" ${settings?.debug_mode ? 'checked' : ''}>
                                            <label class="form-check-label">Включить режим отладки</label>
                                        </div>
                                        <div class="form-text">Включает подробное логирование</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">Уровень логирования</label>
                                        <select class="form-select" name="log_level">
                                            <option value="DEBUG" ${settings?.log_level === 'DEBUG' ? 'selected' : ''}>DEBUG</option>
                                            <option value="INFO" ${settings?.log_level === 'INFO' ? 'selected' : ''}>INFO</option>
                                            <option value="WARNING" ${settings?.log_level === 'WARNING' ? 'selected' : ''}>WARNING</option>
                                            <option value="ERROR" ${settings?.log_level === 'ERROR' ? 'selected' : ''}>ERROR</option>
                                            <option value="CRITICAL" ${settings?.log_level === 'CRITICAL' ? 'selected' : ''}>CRITICAL</option>
                                        </select>
                                        <div class="form-text">Минимальный уровень логирования</div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle"></i> Сохранить настройки
                                </button>
                                <div class="text-muted small">
                                    <i class="bi bi-info-circle"></i> Настройки будут сохранены в базу данных и файл .env
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            container.innerHTML = html;

            // Обработчик формы настроек
            document.getElementById('settingsForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.saveSettings(new FormData(e.target));
            });

        } catch (error) {
            console.error('Ошибка загрузки настроек:', error);
            this.showError('Ошибка загрузки настроек');
        }
    }

    /**
     * Сохранение настроек
     */
    async saveSettings(formData) {
        try {
            const data = Object.fromEntries(formData);

            // Обрабатываем чекбоксы (они не попадают в FormData если не отмечены)
            const form = document.getElementById('settingsForm');
            data.debug_mode = form.querySelector('input[name="debug_mode"]').checked;

            // Преобразуем sms_timeout в число
            if (data.sms_timeout) {
                data.sms_timeout = parseInt(data.sms_timeout);
            }

            console.log('Сохранение настроек:', data);

            await this.apiRequest('/api/admin/settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });

            this.showSuccess('Настройки сохранены успешно');

        } catch (error) {
            console.error('Ошибка сохранения настроек:', error);
            this.showError('Ошибка сохранения настроек');
        }
    }

    /**
     * Загрузка логов - УДАЛЕНО: теперь используется пагинация через logsPagination
     */

    /**
     * Загрузка активаций - УДАЛЕНО: теперь используется пагинация через activationsPagination
     */

    // УДАЛЕНО: Функции управления сервисами - можно создать отдельный модуль /modules/services.js
    // - showServiceModal()
    // - loadServiceData()
    // - saveService()
    // - editService()
    // - deleteService()

    // УДАЛЕНО: Функции управления странами - можно создать отдельный модуль /modules/countries.js
    // - showCountryModal()
    // - loadCountryData()
    // - saveCountry()
    // - editCountry()
    // - deleteCountry()

    // УДАЛЕНО: Функции управления ценами - можно создать отдельный модуль /modules/prices.js
    // - showPriceModal()
    // - loadPriceData()
    // - savePrice()
    // - editPrice()
    // - deletePrice()

    // УДАЛЕНО: Функции управления провайдерами - можно создать отдельный модуль /modules/providers.js
    // - loadProviders()

    // УДАЛЕНО: Остальные функции управления провайдерами - можно создать отдельный модуль /modules/providers.js
    // - showProviderModal()
    // - loadProviderData()
    // - onApiFormatChange()
    // - saveProvider()
    // - editProvider()
    // - deleteProvider()

    // УДАЛЕНО: Все функции управления провайдерами - можно создать отдельный модуль /modules/providers.js
    // - toggleProvider()
    // - getProviderCheckbox()
    // - addLoadingSpinner()
    // - removeLoadingSpinner()
    // - updateProviderVisualState()
    // - revertProviderToggle()
    // - updatePriority()
    // - testProvider()
    // - testProviderConnection()
    // - testAllProviders()
    // - showTestResultsModal()
    // - reloadProviders()

    // УДАЛЕНО: API функции перенесены в /core/api.js
    // - apiRequest()

    // УДАЛЕНО: Функции авторизации перенесены в /core/auth.js
    // - showLoginModal()
    // - login()
    // - logout()

    // УДАЛЕНО: Функции уведомлений перенесены в /core/ui.js
    // - showError()
    // - showSuccess()
    // - showAlert()

    // УДАЛЕНО: Функции форматирования перенесены в /utils/formatters.js
    // - getStatusColor()
    // - formatDate()

    // УДАЛЕНО: Утилиты перенесены в /core/ui.js
    // - copyToClipboard()
    // - togglePasswordVisibility()

    // УДАЛЕНО: Функции управления пользователями перенесены в /modules/users.js
    // - showApiKey()
    // - regenerateApiKey()
    // - editUser()
    // - topupUser()
    // - deductUser()
    // - showUserModal()
    // - saveUser()
}

// Инициализация панели администратора
let adminPanel;

// Инициализация происходит после загрузки всех модулей
// Обработчик в шаблоне вызовет эту функцию
function initAdminPanel() {
    adminPanel = new AdminPanel();
    window.adminPanel = adminPanel; // Делаем доступным глобально
}

// Экспортируем функцию инициализации
window.initAdminPanel = initAdminPanel;
