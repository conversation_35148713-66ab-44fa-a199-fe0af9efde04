/**
 * Универсальный класс для управления пагинацией
 * Обеспечивает постраничную навигацию для больших списков данных
 */
class PaginationManager {
    constructor(containerId, loadDataCallback, options = {}) {
        this.containerId = containerId; // ID контейнера где выводится контент
        this.loadDataCallback = loadDataCallback; // Функция загрузки данных
        this.currentPage = 1; // Текущая страница
        this.pageSize = options.pageSize || 20; // Количество элементов на странице
        this.totalItems = 0; // Общее количество элементов
        this.totalPages = 0; // Общее количество страниц
        this.loading = false; // Флаг загрузки
        this.pageSizeOptions = options.pageSizeOptions || [20, 50, 100, 200]; // Доступные размеры страниц
        this.showInfo = options.showInfo !== false; // Показывать ли информацию о записях
    }

    /**
     * Загрузка данных с учетом пагинации
     * @param {Object} filters - Дополнительные фильтры для запроса
     */
    async loadData(filters = {}) {
        if (this.loading) return;

        this.loading = true;
        this.showLoading();

        try {
            const skip = (this.currentPage - 1) * this.pageSize;
            const params = {
                skip,
                limit: this.pageSize,
                ...filters
            };

            const response = await this.loadDataCallback(params);

            // Если ответ содержит информацию о пагинации
            if (response && typeof response === 'object' && 'data' in response) {
                this.totalItems = response.total || 0;
                this.renderContent(response.data || []);
            } else {
                // Если это простой массив без информации о пагинации
                this.renderContent(response || []);
                this.totalItems = response?.length || 0;
            }

            this.totalPages = Math.ceil(this.totalItems / this.pageSize);
            this.renderPagination();

        } catch (error) {
            console.error('Ошибка загрузки данных:', error);
            this.renderError('Ошибка загрузки данных: ' + error.message);
        } finally {
            this.loading = false;
        }
    }

    /**
     * Переход на указанную страницу
     * @param {number} page - Номер страницы
     * @param {Object} filters - Дополнительные фильтры
     */
    async goToPage(page, filters = {}) {
        if (page < 1 || page > this.totalPages || page === this.currentPage) return;

        this.currentPage = page;
        await this.loadData(filters);
    }

    /**
     * Изменение размера страницы
     * @param {number} newSize - Новый размер страницы
     * @param {Object} filters - Дополнительные фильтры
     */
    async changePageSize(newSize, filters = {}) {
        this.pageSize = parseInt(newSize);
        this.currentPage = 1; // Сбрасываем на первую страницу
        await this.loadData(filters);
    }

    /**
     * Обновление данных (с сохранением текущей страницы)
     * @param {Object} filters - Дополнительные фильтры
     */
    async refresh(filters = {}) {
        await this.loadData(filters);
    }

    /**
     * Создание HTML для пагинации
     */
    renderPagination() {
        const container = document.getElementById(this.containerId);
        if (!container) return;

        // Находим или создаем контейнер для пагинации
        let paginationContainer = container.querySelector('.pagination-container');
        if (!paginationContainer) {
            paginationContainer = document.createElement('div');
            paginationContainer.className = 'pagination-container mt-3';
            container.appendChild(paginationContainer);
        }

        if (this.totalItems === 0) {
            paginationContainer.innerHTML = '';
            return;
        }

        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, this.totalItems);

        let html = '<div class="row align-items-center">';

        // Информация о записях и селектор размера страницы
        if (this.showInfo) {
            html += `
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <span class="text-muted me-3">
                            Показано ${startItem}-${endItem} из ${this.totalItems} записей
                        </span>
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0 small">Записей на странице:</label>
                            <select class="form-select form-select-sm" style="width: auto;"
                                    onchange="this.paginationManager.changePageSize(this.value)">
                                ${this.pageSizeOptions.map(size =>
                                    `<option value="${size}" ${size === this.pageSize ? 'selected' : ''}>${size}</option>`
                                ).join('')}
                            </select>
                        </div>
                    </div>
                </div>
            `;
        }

        // Навигация по страницам
        html += '<div class="col-md-6">';
        if (this.totalPages > 1) {
            html += this.renderPageNavigation();
        }
        html += '</div></div>';

        paginationContainer.innerHTML = html;

        // Сохраняем ссылку на менеджер пагинации для использования в onclick
        paginationContainer.paginationManager = this;

        // Обновляем ссылки на селектор размера страницы
        const pageSizeSelect = paginationContainer.querySelector('select');
        if (pageSizeSelect) {
            pageSizeSelect.paginationManager = this;
        }
    }

    /**
     * Создание навигации по страницам
     * @returns {string} HTML код навигации
     */
    renderPageNavigation() {
        let html = '<nav aria-label="Навигация по страницам">';
        html += '<ul class="pagination pagination-sm justify-content-end mb-0">';

        // Кнопка "Предыдущая"
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="event.preventDefault();
                   this.closest('.pagination-container').paginationManager.goToPage(${this.currentPage - 1})">
                    <i class="bi bi-chevron-left"></i>
                </a>
            </li>
        `;

        // Номера страниц
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Первая страница и многоточие
        if (startPage > 1) {
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="event.preventDefault();
                       this.closest('.pagination-container').paginationManager.goToPage(1)">1</a>
                </li>
            `;
            if (startPage > 2) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // Номера страниц
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="event.preventDefault();
                       this.closest('.pagination-container').paginationManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // Последняя страница и многоточие
        if (endPage < this.totalPages) {
            if (endPage < this.totalPages - 1) {
                html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
            html += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="event.preventDefault();
                       this.closest('.pagination-container').paginationManager.goToPage(${this.totalPages})">${this.totalPages}</a>
                </li>
            `;
        }

        // Кнопка "Следующая"
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="event.preventDefault();
                   this.closest('.pagination-container').paginationManager.goToPage(${this.currentPage + 1})">
                    <i class="bi bi-chevron-right"></i>
                </a>
            </li>
        `;

        html += '</ul></nav>';
        return html;
    }

    /**
     * Отображение контента (переопределяется в наследниках)
     * @param {Array} data - Массив данных для отображения
     */
    renderContent(data) {
        // Базовая реализация - просто выводим данные
        console.log('Загружено элементов:', data.length);
    }

    /**
     * Отображение ошибки
     * @param {string} message - Текст ошибки
     */
    renderError(message) {
        const container = document.getElementById(this.containerId);
        if (container) {
            container.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle"></i> ${message}
                </div>
            `;
        }
    }

    /**
     * Показать индикатор загрузки
     */
    showLoading() {
        const container = document.getElementById(this.containerId);
        if (container) {
            // Находим контейнер для контента (исключая пагинацию)
            let contentContainer = container.querySelector('.content-wrapper');
            if (!contentContainer) {
                contentContainer = container;
            }
            
            contentContainer.innerHTML = `
                <div class="text-center p-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Загрузка...</span>
                    </div>
                    <p class="mt-2 text-muted">Загрузка данных...</p>
                </div>
            `;
        }
    }

    /**
     * Получение текущей страницы
     * @returns {number} Номер текущей страницы
     */
    getCurrentPage() {
        return this.currentPage;
    }

    /**
     * Получение размера страницы
     * @returns {number} Размер страницы
     */
    getPageSize() {
        return this.pageSize;
    }

    /**
     * Получение общего количества элементов
     * @returns {number} Общее количество элементов
     */
    getTotalItems() {
        return this.totalItems;
    }

    /**
     * Получение общего количества страниц
     * @returns {number} Общее количество страниц
     */
    getTotalPages() {
        return this.totalPages;
    }
}
