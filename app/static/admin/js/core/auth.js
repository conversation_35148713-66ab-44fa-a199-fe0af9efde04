/**
 * Модуль авторизации
 * Управляет аутентификацией пользователей в админ панели
 */
class AuthManager {
    constructor() {
        this.isAuthenticated = false;
        this.currentApiKey = '';
        this.callbacks = {
            onLogin: [],
            onLogout: [],
            onError: []
        };
    }

    /**
     * Инициализация менеджера авторизации
     */
    init() {
        console.log('Инициализация менеджера авторизации');
        
        // Проверяем сохраненный API ключ
        const savedApiKey = localStorage.getItem('admin_api_key');
        if (savedApiKey) {
            console.log('Найден сохраненный API ключ, проверяем...');
            this.verifyApiKey(savedApiKey);
        } else {
            console.log('API ключ не найден, показываем форму авторизации');
            this.showAuthForm();
        }

        this.setupEventListeners();
    }

    /**
     * Настройка обработчиков событий
     */
    setupEventListeners() {
        // Обработчик для основной формы авторизации
        const loginButton = document.getElementById('loginButton');
        const apiKeyField = document.getElementById('adminApiKey');

        if (loginButton) {
            loginButton.addEventListener('click', async () => {
                const apiKey = apiKeyField.value.trim();
                if (apiKey) {
                    await this.login(apiKey);
                } else {
                    this.showError('Введите API ключ администратора');
                }
            });
        }

        if (apiKeyField) {
            // Обработка Enter в поле ввода
            apiKeyField.addEventListener('keypress', async (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const apiKey = apiKeyField.value.trim();
                    if (apiKey) {
                        await this.login(apiKey);
                    } else {
                        this.showError('Введите API ключ администратора');
                    }
                }
            });

            // Очистка ошибок при вводе
            apiKeyField.addEventListener('input', () => {
                this.hideError();
            });

            // Фокус при клике
            apiKeyField.addEventListener('click', () => {
                apiKeyField.focus();
            });
        }

        // Обработчик для формы документации
        const docsLoginForm = document.getElementById('docsLoginForm');
        if (docsLoginForm) {
            docsLoginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                const apiKey = document.getElementById('docsApiKey').value.trim();
                if (apiKey) {
                    await this.login(apiKey, true); // true для документации
                } else {
                    this.showError('Введите API ключ администратора', true);
                }
            });
        }
    }

    /**
     * Вход в систему
     * @param {string} apiKey - API ключ
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    async login(apiKey, isDocsPage = false) {
        console.log('Попытка входа в систему');

        // Показываем индикатор загрузки
        this.showLoading(isDocsPage);

        try {
            const isValid = await apiClient.verifyApiKey(apiKey);
            
            if (isValid) {
                console.log('API ключ валидный, авторизация успешна');
                this.currentApiKey = apiKey;
                this.isAuthenticated = true;
                
                // Сохраняем ключ в API клиенте и localStorage
                apiClient.setApiKey(apiKey);
                
                // Показываем главное приложение
                this.showMainApp(isDocsPage);
                
                // Вызываем колбэки успешного входа
                this.triggerCallbacks('onLogin', { apiKey, isDocsPage });
                
            } else {
                console.log('API ключ невалидный');
                this.showError('Неверный ключ администратора', isDocsPage);
                this.logout();
            }
        } catch (error) {
            console.error('Ошибка при входе:', error);
            this.showError('Ошибка проверки ключа администратора', isDocsPage);
            this.logout();
        } finally {
            this.hideLoading(isDocsPage);
        }
    }

    /**
     * Выход из системы
     */
    logout() {
        console.log('Выход из системы');
        
        this.currentApiKey = '';
        this.isAuthenticated = false;
        
        // Очищаем API ключ
        apiClient.clearApiKey();
        
        // Показываем форму авторизации
        this.showAuthForm();
        
        // Вызываем колбэки выхода
        this.triggerCallbacks('onLogout');
    }

    /**
     * Проверка API ключа
     * @param {string} apiKey - API ключ для проверки
     */
    async verifyApiKey(apiKey) {
        try {
            const isValid = await apiClient.verifyApiKey(apiKey);
            
            if (isValid) {
                this.currentApiKey = apiKey;
                this.isAuthenticated = true;
                apiClient.setApiKey(apiKey);
                this.showMainApp();
                this.triggerCallbacks('onLogin', { apiKey });
            } else {
                this.logout();
            }
        } catch (error) {
            console.error('Ошибка проверки API ключа:', error);
            this.logout();
        }
    }

    /**
     * Показать форму авторизации
     */
    showAuthForm() {
        const authForm = document.getElementById('authForm');
        const mainApp = document.getElementById('mainApp');
        const swaggerUI = document.getElementById('swagger-ui');

        if (authForm) {
            authForm.classList.remove('hidden');
        }
        if (mainApp) {
            mainApp.classList.add('hidden');
        }
        if (swaggerUI) {
            swaggerUI.classList.add('hidden');
        }

        // Очищаем и фокусируем поле ввода
        const apiKeyField = document.getElementById('adminApiKey') || document.getElementById('docsApiKey');
        if (apiKeyField) {
            apiKeyField.value = '';
            setTimeout(() => apiKeyField.focus(), 100);
        }
    }

    /**
     * Показать главное приложение
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    showMainApp(isDocsPage = false) {
        const authForm = document.getElementById('authForm');
        
        if (isDocsPage) {
            // Для страницы документации показываем Swagger UI
            const swaggerUI = document.getElementById('swagger-ui');
            if (authForm) authForm.classList.add('hidden');
            if (swaggerUI) {
                swaggerUI.classList.remove('hidden');
                this.initSwaggerUI();
            }
        } else {
            // Для основной админ панели
            const mainApp = document.getElementById('mainApp');
            if (authForm) authForm.classList.add('hidden');
            if (mainApp) {
                mainApp.classList.remove('hidden');
                mainApp.classList.add('show');
            }
        }
    }

    /**
     * Инициализация Swagger UI для документации
     */
    initSwaggerUI() {
        if (typeof SwaggerUIBundle !== 'undefined') {
            SwaggerUIBundle({
                url: '/admin/openapi.json',
                dom_id: '#swagger-ui',
                layout: 'BaseLayout',
                deepLinking: true,
                showExtensions: true,
                showCommonExtensions: true,
                requestInterceptor: (request) => {
                    request.headers['X-API-Key'] = this.currentApiKey;
                    return request;
                }
            });
        }
    }

    /**
     * Показать ошибку
     * @param {string} message - Текст ошибки
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    showError(message, isDocsPage = false) {
        const errorId = isDocsPage ? 'docsError' : 'loginError';
        const errorDiv = document.getElementById(errorId);
        
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.classList.remove('hidden');
            
            // Автоматически скрываем ошибку через 5 секунд
            setTimeout(() => this.hideError(isDocsPage), 5000);
        }
        
        this.triggerCallbacks('onError', { message, isDocsPage });
    }

    /**
     * Скрыть ошибку
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    hideError(isDocsPage = false) {
        const errorId = isDocsPage ? 'docsError' : 'loginError';
        const errorDiv = document.getElementById(errorId);
        
        if (errorDiv) {
            errorDiv.classList.add('hidden');
        }
    }

    /**
     * Показать индикатор загрузки
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    showLoading(isDocsPage = false) {
        const buttonId = isDocsPage ? 'docsLoginButton' : 'loginButton';
        const button = document.getElementById(buttonId);
        
        if (button) {
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Проверка...';
            button.disabled = true;
        }
    }

    /**
     * Скрыть индикатор загрузки
     * @param {boolean} isDocsPage - флаг страницы документации
     */
    hideLoading(isDocsPage = false) {
        const buttonId = isDocsPage ? 'docsLoginButton' : 'loginButton';
        const button = document.getElementById(buttonId);
        
        if (button && button.dataset.originalText) {
            button.innerHTML = button.dataset.originalText;
            button.disabled = false;
        }
    }

    /**
     * Добавить колбэк для события
     * @param {string} event - Название события (onLogin, onLogout, onError)
     * @param {Function} callback - Функция колбэка
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * Вызвать колбэки для события
     * @param {string} event - Название события
     * @param {Object} data - Данные для передачи в колбэки
     */
    triggerCallbacks(event, data = {}) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Ошибка в колбэке ${event}:`, error);
                }
            });
        }
    }

    /**
     * Проверка авторизации
     * @returns {boolean} true если пользователь авторизован
     */
    isLoggedIn() {
        return this.isAuthenticated && this.currentApiKey;
    }

    /**
     * Получение текущего API ключа
     * @returns {string} Текущий API ключ
     */
    getCurrentApiKey() {
        return this.currentApiKey;
    }
}

// Создаем глобальный экземпляр менеджера авторизации
window.authManager = new AuthManager();
