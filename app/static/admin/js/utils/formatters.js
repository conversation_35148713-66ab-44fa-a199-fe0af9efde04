/**
 * Утилиты для форматирования данных
 * Содержит функции для форматирования различных типов данных
 */
class DataFormatters {
    /**
     * Форматирование даты и времени
     * @param {string|Date} dateInput - Дата для форматирования
     * @param {Object} options - Опции форматирования
     * @returns {string} Отформатированная дата
     */
    static formatDate(dateInput, options = {}) {
        if (!dateInput) return '-';
        
        try {
            const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
            
            const defaultOptions = {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                timeZone: 'Europe/Moscow'
            };
            
            const formatOptions = { ...defaultOptions, ...options };
            
            return date.toLocaleString('ru-RU', formatOptions);
        } catch (error) {
            console.error('Ошибка форматирования даты:', error);
            return String(dateInput);
        }
    }

    /**
     * Форматирование даты без времени
     * @param {string|Date} dateInput - Дата для форматирования
     * @returns {string} Отформатированная дата
     */
    static formatDateOnly(dateInput) {
        return this.formatDate(dateInput, {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    }

    /**
     * Форматирование времени
     * @param {string|Date} dateInput - Дата для форматирования
     * @returns {string} Отформатированное время
     */
    static formatTimeOnly(dateInput) {
        return this.formatDate(dateInput, {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    /**
     * Форматирование относительного времени (например, "2 часа назад")
     * @param {string|Date} dateInput - Дата для форматирования
     * @returns {string} Относительное время
     */
    static formatRelativeTime(dateInput) {
        if (!dateInput) return '-';
        
        try {
            const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
            const now = new Date();
            const diffMs = now - date;
            const diffMinutes = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffMinutes < 1) {
                return 'только что';
            } else if (diffMinutes < 60) {
                return `${diffMinutes} мин. назад`;
            } else if (diffHours < 24) {
                return `${diffHours} ч. назад`;
            } else if (diffDays < 7) {
                return `${diffDays} дн. назад`;
            } else {
                return this.formatDate(dateInput);
            }
        } catch (error) {
            console.error('Ошибка форматирования относительного времени:', error);
            return String(dateInput);
        }
    }

    /**
     * Форматирование денежных сумм
     * @param {number|string} amount - Сумма для форматирования
     * @param {string} currency - Валюта (по умолчанию USD)
     * @param {number} decimals - Количество знаков после запятой
     * @returns {string} Отформатированная сумма
     */
    static formatMoney(amount, currency = 'USD', decimals = 2) {
        if (amount === null || amount === undefined) return '-';
        
        try {
            const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
            
            if (isNaN(numAmount)) return '-';
            
            const formatted = numAmount.toFixed(decimals);
            
            switch (currency.toLowerCase()) {
                case 'usd':
                    return `$${formatted}`;
                case 'eur':
                    return `€${formatted}`;
                case 'rub':
                    return `${formatted} ₽`;
                default:
                    return `${formatted} ${currency}`;
            }
        } catch (error) {
            console.error('Ошибка форматирования денежной суммы:', error);
            return String(amount);
        }
    }

    /**
     * Форматирование процентов
     * @param {number|string} value - Значение для форматирования
     * @param {number} decimals - Количество знаков после запятой
     * @returns {string} Отформатированный процент
     */
    static formatPercent(value, decimals = 1) {
        if (value === null || value === undefined) return '-';
        
        try {
            const numValue = typeof value === 'string' ? parseFloat(value) : value;
            
            if (isNaN(numValue)) return '-';
            
            return `${numValue.toFixed(decimals)}%`;
        } catch (error) {
            console.error('Ошибка форматирования процента:', error);
            return String(value);
        }
    }

    /**
     * Форматирование номера телефона
     * @param {string} phone - Номер телефона
     * @returns {string} Отформатированный номер
     */
    static formatPhone(phone) {
        if (!phone) return '-';
        
        try {
            // Убираем все нецифровые символы
            const cleaned = phone.replace(/\D/g, '');
            
            if (cleaned.length === 0) return '-';
            
            // Если номер начинается с 7 или 8 (российский)
            if (cleaned.length === 11 && (cleaned[0] === '7' || cleaned[0] === '8')) {
                return `+7 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7, 9)}-${cleaned.slice(9)}`;
            }
            
            // Для других номеров просто добавляем + в начало
            return `+${cleaned}`;
        } catch (error) {
            console.error('Ошибка форматирования номера телефона:', error);
            return String(phone);
        }
    }

    /**
     * Форматирование размера файла
     * @param {number} bytes - Размер в байтах
     * @param {number} decimals - Количество знаков после запятой
     * @returns {string} Отформатированный размер
     */
    static formatFileSize(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        if (!bytes) return '-';
        
        try {
            const k = 1024;
            const dm = decimals < 0 ? 0 : decimals;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
        } catch (error) {
            console.error('Ошибка форматирования размера файла:', error);
            return String(bytes);
        }
    }

    /**
     * Форматирование числа с разделителями тысяч
     * @param {number|string} number - Число для форматирования
     * @param {string} separator - Разделитель тысяч
     * @returns {string} Отформатированное число
     */
    static formatNumber(number, separator = ' ') {
        if (number === null || number === undefined) return '-';
        
        try {
            const numValue = typeof number === 'string' ? parseFloat(number) : number;
            
            if (isNaN(numValue)) return '-';
            
            return numValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, separator);
        } catch (error) {
            console.error('Ошибка форматирования числа:', error);
            return String(number);
        }
    }

    /**
     * Обрезка текста с добавлением многоточия
     * @param {string} text - Текст для обрезки
     * @param {number} maxLength - Максимальная длина
     * @param {string} suffix - Суффикс (по умолчанию ...)
     * @returns {string} Обрезанный текст
     */
    static truncateText(text, maxLength, suffix = '...') {
        if (!text) return '-';
        
        if (text.length <= maxLength) return text;
        
        return text.substring(0, maxLength - suffix.length) + suffix;
    }

    /**
     * Форматирование статуса с цветом
     * @param {string} status - Статус
     * @returns {Object} Объект с текстом и цветом
     */
    static formatStatus(status) {
        if (!status) return { text: '-', color: 'secondary' };
        
        const statusMap = {
            'PENDING': { text: 'Ожидание', color: 'warning' },
            'WAITING_SMS': { text: 'Ожидание SMS', color: 'info' },
            'COMPLETED': { text: 'Завершено', color: 'success' },
            'CANCELLED': { text: 'Отменено', color: 'danger' },
            'EXPIRED': { text: 'Истекло', color: 'secondary' },
            'ERROR': { text: 'Ошибка', color: 'danger' },
            'ACTIVE': { text: 'Активно', color: 'success' },
            'INACTIVE': { text: 'Неактивно', color: 'secondary' },
            'TOPUP': { text: 'Пополнение', color: 'success' },
            'DEDUCT': { text: 'Списание', color: 'warning' }
        };
        
        return statusMap[status.toUpperCase()] || { text: status, color: 'secondary' };
    }

    /**
     * Форматирование роли пользователя
     * @param {string} role - Роль пользователя
     * @returns {Object} Объект с текстом и цветом
     */
    static formatUserRole(role) {
        if (!role) return { text: '-', color: 'secondary' };
        
        const roleMap = {
            'admin': { text: 'Администратор', color: 'danger' },
            'user': { text: 'Пользователь', color: 'primary' },
            'moderator': { text: 'Модератор', color: 'warning' }
        };
        
        return roleMap[role.toLowerCase()] || { text: role, color: 'secondary' };
    }

    /**
     * Экранирование HTML
     * @param {string} text - Текст для экранирования
     * @returns {string} Экранированный текст
     */
    static escapeHtml(text) {
        if (!text) return '';
        
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, (m) => map[m]);
    }

    /**
     * Форматирование IP адреса
     * @param {string} ip - IP адрес
     * @returns {string} Отформатированный IP адрес
     */
    static formatIpAddress(ip) {
        if (!ip) return '-';
        
        // Простая проверка на валидность IPv4
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (ipv4Regex.test(ip)) {
            return ip;
        }
        
        // Для IPv6 или других форматов возвращаем как есть
        return ip;
    }
}

// Экспортируем класс в глобальную область
window.DataFormatters = DataFormatters;

// Функции-обертки для совместимости с существующим кодом
window.formatDate = (date) => DataFormatters.formatDate(date);
window.formatMoney = (amount, currency = 'USD') => DataFormatters.formatMoney(amount, currency);
window.formatNumber = (number) => DataFormatters.formatNumber(number);
window.formatPhone = (phone) => DataFormatters.formatPhone(phone);
window.formatPercent = (value, decimals = 1) => DataFormatters.formatPercent(value, decimals);
window.formatFileSize = (bytes) => DataFormatters.formatFileSize(bytes);
window.truncateText = (text, maxLength, suffix = '...') => DataFormatters.truncateText(text, maxLength, suffix);
window.escapeHtml = (text) => DataFormatters.escapeHtml(text);

// Функции для получения цветов статусов
window.getStatusColor = (status) => {
    const statusInfo = DataFormatters.formatStatus(status);
    return statusInfo.color;
};

window.getBooleanColor = (value) => value ? 'success' : 'secondary';

// Дополнительные функции форматирования
window.formatPrice = (price) => {
    if (price === null || price === undefined || price === 0) return '-';
    return DataFormatters.formatMoney(price, 'USD');
};

window.formatBoolean = (value) => value ? 'Да' : 'Нет';
