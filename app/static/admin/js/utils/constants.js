/**
 * Константы для админ панели
 * Содержит все константы, используемые в приложении
 */
class AppConstants {
    // API endpoints
    static API_ENDPOINTS = {
        // Авторизация
        VERIFY_API_KEY: '/admin/verify-api-key',
        
        // Статистика
        STATISTICS: '/api/admin/statistics',
        
        // Пользователи
        USERS: '/api/admin/users',
        USER_BY_ID: (id) => `/api/admin/users/${id}`,
        USER_TOPUP: (id) => `/api/admin/users/${id}/topup`,
        USER_DEDUCT: (id) => `/api/admin/users/${id}/deduct`,
        USER_API_KEY: (id) => `/api/admin/users/${id}/api-key`,
        USER_REGENERATE_API_KEY: (id) => `/api/admin/users/${id}/regenerate-api-key`,
        
        // Активации
        ACTIVATIONS: '/api/admin/activations',
        ACTIVATION_BY_ID: (id) => `/api/admin/activations/${id}`,
        
        // Транзакции
        TRANSACTIONS: '/api/admin/transactions',
        TRANSACTION_BY_ID: (id) => `/api/admin/transactions/${id}`,
        
        // Сервисы
        SERVICES: '/api/admin/services',
        SERVICE_BY_ID: (id) => `/api/admin/services/${id}`,
        
        // Страны
        COUNTRIES: '/api/admin/countries',
        COUNTRY_BY_ID: (id) => `/api/admin/countries/${id}`,
        
        // Цены
        PRICES: '/api/admin/prices',
        PRICE_BY_ID: (id) => `/api/admin/prices/${id}`,
        
        // Логи
        LOGS: '/api/admin/logs',
        
        // Настройки
        SETTINGS: '/api/admin/settings',
        
        // Провайдеры
        PROVIDERS: '/api/admin/providers',
        PROVIDER_BY_ID: (id) => `/api/admin/providers/${id}`,
        
        // Документация
        OPENAPI_JSON: '/admin/openapi.json'
    };

    // Статусы активаций
    static ACTIVATION_STATUSES = {
        PENDING: 'PENDING',
        WAITING_SMS: 'WAITING_SMS',
        COMPLETED: 'COMPLETED',
        CANCELLED: 'CANCELLED',
        EXPIRED: 'EXPIRED',
        ERROR: 'ERROR'
    };

    // Типы транзакций
    static TRANSACTION_TYPES = {
        TOPUP: 'TOPUP',
        DEDUCT: 'DEDUCT'
    };

    // Роли пользователей
    static USER_ROLES = {
        ADMIN: 'admin',
        USER: 'user',
        MODERATOR: 'moderator'
    };

    // Уровни логирования
    static LOG_LEVELS = {
        DEBUG: 'DEBUG',
        INFO: 'INFO',
        WARNING: 'WARNING',
        ERROR: 'ERROR',
        CRITICAL: 'CRITICAL'
    };

    // Цвета для статусов
    static STATUS_COLORS = {
        PENDING: 'warning',
        WAITING_SMS: 'info',
        COMPLETED: 'success',
        CANCELLED: 'danger',
        EXPIRED: 'secondary',
        ERROR: 'danger',
        ACTIVE: 'success',
        INACTIVE: 'secondary'
    };

    // Цвета для ролей
    static ROLE_COLORS = {
        admin: 'danger',
        user: 'primary',
        moderator: 'warning'
    };

    // Цвета для типов транзакций
    static TRANSACTION_COLORS = {
        TOPUP: 'success',
        DEDUCT: 'warning'
    };

    // Настройки пагинации по умолчанию
    static PAGINATION_DEFAULTS = {
        PAGE_SIZE: 20,
        PAGE_SIZE_OPTIONS: [20, 50, 100, 200],
        MAX_VISIBLE_PAGES: 5
    };

    // Настройки уведомлений
    static NOTIFICATION_DEFAULTS = {
        DURATION: 5000,
        AUTO_HIDE: true
    };

    // Настройки автообновления
    static AUTO_REFRESH = {
        DASHBOARD_INTERVAL: 30000, // 30 секунд
        ENABLED: true
    };

    // Валидация
    static VALIDATION = {
        API_KEY_MIN_LENGTH: 10,
        USERNAME_MIN_LENGTH: 3,
        USERNAME_MAX_LENGTH: 50,
        PASSWORD_MIN_LENGTH: 6,
        EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        PHONE_REGEX: /^\+?[\d\s\-\(\)]+$/,
        SERVICE_CODE_MAX_LENGTH: 10,
        COUNTRY_CODE_MAX_LENGTH: 5
    };

    // Форматы дат
    static DATE_FORMATS = {
        FULL: {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        },
        DATE_ONLY: {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        },
        TIME_ONLY: {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }
    };

    // Локализация
    static LOCALE = 'ru-RU';
    static TIMEZONE = 'Europe/Moscow';

    // Размеры файлов
    static FILE_SIZES = {
        KB: 1024,
        MB: 1024 * 1024,
        GB: 1024 * 1024 * 1024
    };

    // HTTP статус коды
    static HTTP_STATUS = {
        OK: 200,
        CREATED: 201,
        NO_CONTENT: 204,
        BAD_REQUEST: 400,
        UNAUTHORIZED: 401,
        FORBIDDEN: 403,
        NOT_FOUND: 404,
        INTERNAL_SERVER_ERROR: 500
    };

    // Сообщения об ошибках
    static ERROR_MESSAGES = {
        NETWORK_ERROR: 'Ошибка сети. Проверьте подключение к интернету.',
        UNAUTHORIZED: 'Не авторизован. Войдите в систему.',
        FORBIDDEN: 'Доступ запрещен.',
        NOT_FOUND: 'Ресурс не найден.',
        SERVER_ERROR: 'Ошибка сервера. Попробуйте позже.',
        VALIDATION_ERROR: 'Ошибка валидации данных.',
        UNKNOWN_ERROR: 'Неизвестная ошибка.'
    };

    // Сообщения об успехе
    static SUCCESS_MESSAGES = {
        USER_CREATED: 'Пользователь успешно создан',
        USER_UPDATED: 'Пользователь успешно обновлен',
        USER_DELETED: 'Пользователь успешно удален',
        SERVICE_CREATED: 'Сервис успешно создан',
        SERVICE_UPDATED: 'Сервис успешно обновлен',
        SERVICE_DELETED: 'Сервис успешно удален',
        COUNTRY_CREATED: 'Страна успешно создана',
        COUNTRY_UPDATED: 'Страна успешно обновлена',
        COUNTRY_DELETED: 'Страна успешно удалена',
        PRICE_CREATED: 'Цена успешно создана',
        PRICE_UPDATED: 'Цена успешно обновлена',
        PRICE_DELETED: 'Цена успешно удалена',
        SETTINGS_SAVED: 'Настройки сохранены успешно',
        BALANCE_UPDATED: 'Баланс успешно обновлен',
        API_KEY_REGENERATED: 'API ключ успешно обновлен'
    };

    // Подтверждающие сообщения
    static CONFIRM_MESSAGES = {
        DELETE_USER: 'Вы уверены, что хотите удалить этого пользователя?',
        DELETE_SERVICE: 'Вы уверены, что хотите удалить этот сервис?',
        DELETE_COUNTRY: 'Вы уверены, что хотите удалить эту страну?',
        DELETE_PRICE: 'Вы уверены, что хотите удалить эту цену?',
        REGENERATE_API_KEY: 'Вы уверены, что хотите обновить API ключ? Старый ключ перестанет работать.',
        LOGOUT: 'Вы уверены, что хотите выйти из системы?'
    };

    // CSS классы
    static CSS_CLASSES = {
        LOADING: 'loading',
        HIDDEN: 'hidden',
        SHOW: 'show',
        ACTIVE: 'active',
        DISABLED: 'disabled',
        ERROR: 'error',
        SUCCESS: 'success'
    };

    // Селекторы DOM элементов
    static SELECTORS = {
        SIDEBAR: '.sidebar',
        MAIN_CONTENT: '.main-content',
        MOBILE_OVERLAY: '#mobileOverlay',
        SIDEBAR_TOGGLE: '#sidebarToggle',
        AUTH_FORM: '#authForm',
        MAIN_APP: '#mainApp',
        SWAGGER_UI: '#swagger-ui'
    };

    // События
    static EVENTS = {
        LOGIN: 'login',
        LOGOUT: 'logout',
        ERROR: 'error',
        DATA_LOADED: 'dataLoaded',
        SECTION_CHANGED: 'sectionChanged'
    };

    /**
     * Получить цвет для статуса
     * @param {string} status - Статус
     * @returns {string} CSS класс цвета
     */
    static getStatusColor(status) {
        return this.STATUS_COLORS[status] || 'secondary';
    }

    /**
     * Получить цвет для роли
     * @param {string} role - Роль
     * @returns {string} CSS класс цвета
     */
    static getRoleColor(role) {
        return this.ROLE_COLORS[role] || 'secondary';
    }

    /**
     * Получить цвет для типа транзакции
     * @param {string} type - Тип транзакции
     * @returns {string} CSS класс цвета
     */
    static getTransactionColor(type) {
        return this.TRANSACTION_COLORS[type] || 'secondary';
    }

    /**
     * Проверить валидность email
     * @param {string} email - Email для проверки
     * @returns {boolean} true если email валидный
     */
    static isValidEmail(email) {
        return this.VALIDATION.EMAIL_REGEX.test(email);
    }

    /**
     * Проверить валидность номера телефона
     * @param {string} phone - Номер телефона для проверки
     * @returns {boolean} true если номер валидный
     */
    static isValidPhone(phone) {
        return this.VALIDATION.PHONE_REGEX.test(phone);
    }
}

// Экспортируем класс в глобальную область
window.AppConstants = AppConstants;
